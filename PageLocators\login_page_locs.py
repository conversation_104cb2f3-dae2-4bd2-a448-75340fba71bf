from selenium.webdriver.common.by import By



class LoginPageLocs:
    # 选择工厂号登录

    select_account_loc = (By.XPATH, "//*[@id='rc-tabs-0-tab-1']")
    # 用户名输入框
    account_loc = (By.XPATH, '//input[@placeholder="请输入工厂代码"]')
    username_loc = (By.XPATH, '//input[@placeholder="请输入账号"]')
    # 密码输入框
    password_loc = (By.XPATH, '/html/body/div[1]/div/div[2]/div[2]/div[2]/div[2]/div/div[2]/form/div[3]/div[1]/div/div[1]/div/span/input')
    # 登录按钮
    submit_loc = (By.XPATH, '/html/body/div[1]/div/div[2]/div[2]/div[2]/div[2]/div/div[2]/form/div[4]/div/div/div/div/button')
    # submit_loc = (By.XPATH, '//*[@id="rc-tabs-1-panel-1"]/form/div[4]/div/div/div/div/button')
    continue_back = (By.XPATH,'//*[@id="root"]/div/div[2]/div[2]/form/div[3]/div/div/div/div/button[1]/span')

    #login_button_loc = (By.TAG_NAME, 'button')
    #错误提示 /html/body/div[4]/div/div/div/div/div/span[2]
    error_tips_from_login_area = (By.XPATH, "//span[contains(text(), '用户名或密码错误')]")






