"""
跨平台工具模块

提供平台检测、路径处理、环境配置等跨平台兼容的工具函数。
支持Windows、Mac、Linux等主流操作系统。

作者: BlackLake测试框架
版本: 1.0.0
"""

import os
import sys
import platform
import subprocess
from pathlib import Path
from typing import Dict, List, Optional, Tuple, Any
from enum import Enum

from Common.my_logger import logger


class PlatformType(Enum):
    """平台类型枚举"""
    WINDOWS = "windows"
    MAC = "mac"
    LINUX = "linux"
    UNKNOWN = "unknown"


class PlatformUtils:
    """平台工具类"""
    
    @staticmethod
    def get_platform() -> PlatformType:
        """
        获取当前运行平台
        
        Returns:
            PlatformType: 平台类型枚举
        """
        system = platform.system().lower()
        if system == 'darwin':
            return PlatformType.MAC
        elif system == 'windows':
            return PlatformType.WINDOWS
        elif system == 'linux':
            return PlatformType.LINUX
        else:
            logger.warning(f"未知平台: {system}")
            return PlatformType.UNKNOWN
    
    @staticmethod
    def is_windows() -> bool:
        """判断是否为Windows平台"""
        return PlatformUtils.get_platform() == PlatformType.WINDOWS
    
    @staticmethod
    def is_mac() -> bool:
        """判断是否为Mac平台"""
        return PlatformUtils.get_platform() == PlatformType.MAC
    
    @staticmethod
    def is_linux() -> bool:
        """判断是否为Linux平台"""
        return PlatformUtils.get_platform() == PlatformType.LINUX
    
    @staticmethod
    def get_platform_info() -> Dict[str, Any]:
        """
        获取详细的平台信息
        
        Returns:
            Dict[str, Any]: 包含平台详细信息的字典
        """
        return {
            'platform_type': PlatformUtils.get_platform().value,
            'system': platform.system(),
            'release': platform.release(),
            'version': platform.version(),
            'machine': platform.machine(),
            'processor': platform.processor(),
            'architecture': platform.architecture(),
            'python_version': platform.python_version(),
            'python_implementation': platform.python_implementation(),
            'sys_platform': sys.platform,
            'os_name': os.name
        }
    
    @staticmethod
    def get_executable_extension() -> str:
        """
        获取当前平台的可执行文件扩展名
        
        Returns:
            str: 可执行文件扩展名
        """
        if PlatformUtils.is_windows():
            return '.exe'
        else:
            return ''
    
    @staticmethod
    def get_path_separator() -> str:
        """
        获取当前平台的路径分隔符
        
        Returns:
            str: 路径分隔符
        """
        return os.sep
    
    @staticmethod
    def normalize_path(path: str) -> str:
        """
        标准化路径（跨平台兼容）
        
        Args:
            path (str): 原始路径
            
        Returns:
            str: 标准化后的路径
        """
        return os.path.normpath(path)
    
    @staticmethod
    def join_path(*paths) -> str:
        """
        跨平台路径连接
        
        Args:
            *paths: 路径组件
            
        Returns:
            str: 连接后的路径
        """
        return os.path.join(*paths)
    
    @staticmethod
    def get_home_directory() -> str:
        """
        获取用户主目录
        
        Returns:
            str: 用户主目录路径
        """
        return str(Path.home())
    
    @staticmethod
    def get_temp_directory() -> str:
        """
        获取临时目录
        
        Returns:
            str: 临时目录路径
        """
        import tempfile
        return tempfile.gettempdir()


class KeyboardUtils:
    """键盘工具类"""
    
    @staticmethod
    def get_select_all_key() -> str:
        """
        获取全选快捷键
        
        Returns:
            str: 全选快捷键组合
        """
        if PlatformUtils.is_mac():
            return 'cmd+a'
        else:
            return 'ctrl+a'
    
    @staticmethod
    def get_copy_key() -> str:
        """
        获取复制快捷键
        
        Returns:
            str: 复制快捷键组合
        """
        if PlatformUtils.is_mac():
            return 'cmd+c'
        else:
            return 'ctrl+c'
    
    @staticmethod
    def get_paste_key() -> str:
        """
        获取粘贴快捷键
        
        Returns:
            str: 粘贴快捷键组合
        """
        if PlatformUtils.is_mac():
            return 'cmd+v'
        else:
            return 'ctrl+v'
    
    @staticmethod
    def get_selenium_modifier_key():
        """
        获取Selenium使用的修饰键
        
        Returns:
            Keys: Selenium Keys对象
        """
        from selenium.webdriver.common.keys import Keys
        if PlatformUtils.is_mac():
            return Keys.COMMAND
        else:
            return Keys.CONTROL


class EnvironmentUtils:
    """环境工具类"""
    
    @staticmethod
    def get_environment_variables() -> Dict[str, str]:
        """
        获取环境变量
        
        Returns:
            Dict[str, str]: 环境变量字典
        """
        return dict(os.environ)
    
    @staticmethod
    def get_environment_variable(name: str, default: Optional[str] = None) -> Optional[str]:
        """
        获取指定环境变量
        
        Args:
            name (str): 环境变量名
            default (Optional[str]): 默认值
            
        Returns:
            Optional[str]: 环境变量值
        """
        return os.environ.get(name, default)
    
    @staticmethod
    def set_environment_variable(name: str, value: str) -> None:
        """
        设置环境变量
        
        Args:
            name (str): 环境变量名
            value (str): 环境变量值
        """
        os.environ[name] = value
    
    @staticmethod
    def check_command_exists(command: str) -> bool:
        """
        检查命令是否存在
        
        Args:
            command (str): 命令名
            
        Returns:
            bool: 命令是否存在
        """
        try:
            if PlatformUtils.is_windows():
                subprocess.run(['where', command], 
                             check=True, 
                             capture_output=True, 
                             text=True)
            else:
                subprocess.run(['which', command], 
                             check=True, 
                             capture_output=True, 
                             text=True)
            return True
        except subprocess.CalledProcessError:
            return False
    
    @staticmethod
    def get_python_executable() -> str:
        """
        获取Python可执行文件路径
        
        Returns:
            str: Python可执行文件路径
        """
        return sys.executable


class FileUtils:
    """文件工具类"""
    
    @staticmethod
    def ensure_directory_exists(directory: str) -> None:
        """
        确保目录存在
        
        Args:
            directory (str): 目录路径
        """
        Path(directory).mkdir(parents=True, exist_ok=True)
    
    @staticmethod
    def get_file_size(file_path: str) -> int:
        """
        获取文件大小
        
        Args:
            file_path (str): 文件路径
            
        Returns:
            int: 文件大小（字节）
        """
        return os.path.getsize(file_path)
    
    @staticmethod
    def is_file_executable(file_path: str) -> bool:
        """
        检查文件是否可执行
        
        Args:
            file_path (str): 文件路径
            
        Returns:
            bool: 文件是否可执行
        """
        return os.access(file_path, os.X_OK)
    
    @staticmethod
    def make_file_executable(file_path: str) -> None:
        """
        使文件可执行
        
        Args:
            file_path (str): 文件路径
        """
        if not PlatformUtils.is_windows():
            os.chmod(file_path, 0o755)


# 便捷函数
def get_platform() -> PlatformType:
    """获取当前平台类型"""
    return PlatformUtils.get_platform()


def is_windows() -> bool:
    """判断是否为Windows平台"""
    return PlatformUtils.is_windows()


def is_mac() -> bool:
    """判断是否为Mac平台"""
    return PlatformUtils.is_mac()


def is_linux() -> bool:
    """判断是否为Linux平台"""
    return PlatformUtils.is_linux()


def get_platform_info() -> Dict[str, Any]:
    """获取平台详细信息"""
    return PlatformUtils.get_platform_info()


if __name__ == '__main__':
    # 测试代码
    print("=== 平台信息测试 ===")
    print(f"当前平台: {get_platform().value}")
    print(f"是否Windows: {is_windows()}")
    print(f"是否Mac: {is_mac()}")
    print(f"是否Linux: {is_linux()}")
    
    print("\n=== 详细平台信息 ===")
    info = get_platform_info()
    for key, value in info.items():
        print(f"{key}: {value}")
    
    print("\n=== 路径工具测试 ===")
    print(f"可执行文件扩展名: '{PlatformUtils.get_executable_extension()}'")
    print(f"路径分隔符: '{PlatformUtils.get_path_separator()}'")
    print(f"用户主目录: {PlatformUtils.get_home_directory()}")
    print(f"临时目录: {PlatformUtils.get_temp_directory()}")
    
    print("\n=== 键盘快捷键测试 ===")
    print(f"全选快捷键: {KeyboardUtils.get_select_all_key()}")
    print(f"复制快捷键: {KeyboardUtils.get_copy_key()}")
    print(f"粘贴快捷键: {KeyboardUtils.get_paste_key()}")
