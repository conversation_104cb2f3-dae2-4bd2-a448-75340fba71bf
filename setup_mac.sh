#!/bin/bash

# BlackLake测试框架 - Mac平台安装脚本
# 版本: 1.0.0
# 作者: BlackLake Team

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查是否为Mac系统
check_mac_system() {
    if [[ "$OSTYPE" != "darwin"* ]]; then
        log_error "此脚本仅适用于Mac系统！"
        exit 1
    fi
    log_success "检测到Mac系统"
}

# 检查Python版本
check_python() {
    log_info "检查Python环境..."
    
    if command -v python3 &> /dev/null; then
        PYTHON_VERSION=$(python3 --version 2>&1 | awk '{print $2}')
        log_success "找到Python3: $PYTHON_VERSION"
        PYTHON_CMD="python3"
        PIP_CMD="pip3"
    elif command -v python &> /dev/null; then
        PYTHON_VERSION=$(python --version 2>&1 | awk '{print $2}')
        if [[ $PYTHON_VERSION == 3.* ]]; then
            log_success "找到Python: $PYTHON_VERSION"
            PYTHON_CMD="python"
            PIP_CMD="pip"
        else
            log_error "需要Python 3.x版本，当前版本: $PYTHON_VERSION"
            exit 1
        fi
    else
        log_error "未找到Python！请先安装Python 3.x"
        log_info "建议使用Homebrew安装: brew install python"
        exit 1
    fi
}

# 检查并安装Homebrew
check_homebrew() {
    log_info "检查Homebrew..."
    
    if command -v brew &> /dev/null; then
        log_success "Homebrew已安装"
    else
        log_warning "未找到Homebrew，正在安装..."
        /bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"
        log_success "Homebrew安装完成"
    fi
}

# 安装Chrome浏览器
install_chrome() {
    log_info "检查Chrome浏览器..."
    
    if [ -d "/Applications/Google Chrome.app" ]; then
        log_success "Chrome浏览器已安装"
    else
        log_warning "未找到Chrome浏览器，正在安装..."
        brew install --cask google-chrome
        log_success "Chrome浏览器安装完成"
    fi
}

# 创建虚拟环境
create_virtual_env() {
    log_info "创建Python虚拟环境..."
    
    if [ -d "venv" ]; then
        log_warning "虚拟环境已存在，跳过创建"
    else
        $PYTHON_CMD -m venv venv
        log_success "虚拟环境创建完成"
    fi
}

# 激活虚拟环境并安装依赖
install_dependencies() {
    log_info "激活虚拟环境并安装依赖包..."
    
    source venv/bin/activate
    
    # 升级pip
    pip install --upgrade pip
    
    # 安装Mac专用依赖
    if [ -f "requirements_mac.txt" ]; then
        log_info "使用Mac专用依赖配置..."
        pip install -r requirements_mac.txt
    elif [ -f "requirements_web.txt" ]; then
        log_info "使用Web版依赖配置..."
        pip install -r requirements_web.txt
    else
        log_error "未找到依赖配置文件！"
        exit 1
    fi
    
    log_success "依赖包安装完成"
}

# 创建Mac专用配置
create_mac_config() {
    log_info "创建Mac专用配置..."
    
    # 创建Mac配置目录
    mkdir -p Conf/mac
    
    # 创建Mac专用配置文件
    cat > Conf/mac/config_mac.ini << EOF
[log]
account = 009516
name = admin
password = admin
level = INFO
file_ok = True
file_name = blacklake_mac.log

[browser]
default_browser = chrome
headless = False
window_size = 1920,1080
implicit_wait = 10
page_load_timeout = 30

[platform]
os_type = mac
driver_auto_download = True
screenshot_format = png
EOF
    
    log_success "Mac配置文件创建完成"
}

# 创建启动脚本
create_launch_scripts() {
    log_info "创建启动脚本..."
    
    # 创建测试运行脚本
    cat > run_tests_mac.sh << 'EOF'
#!/bin/bash

# BlackLake测试框架 - Mac测试运行脚本

# 激活虚拟环境
source venv/bin/activate

# 设置环境变量
export PYTHONPATH="${PYTHONPATH}:$(pwd)"

# 运行测试
echo "开始运行测试..."
python -m pytest TestCases/ --alluredir=./allure-results -v

# 生成Allure报告
if command -v allure &> /dev/null; then
    echo "生成Allure报告..."
    allure generate ./allure-results -o ./allure-report --clean
    echo "报告生成完成，位置: ./allure-report"
else
    echo "未安装Allure，跳过报告生成"
    echo "可通过以下命令安装: brew install allure"
fi
EOF
    
    chmod +x run_tests_mac.sh
    
    # 创建Web应用启动脚本
    cat > run_web_app_mac.sh << 'EOF'
#!/bin/bash

# BlackLake测试框架 - Mac Web应用启动脚本

# 激活虚拟环境
source venv/bin/activate

# 设置环境变量
export PYTHONPATH="${PYTHONPATH}:$(pwd)"
export FLASK_ENV=development

# 启动Web应用
echo "启动Web应用..."
python web_app.py
EOF
    
    chmod +x run_web_app_mac.sh
    
    log_success "启动脚本创建完成"
}

# 验证安装
verify_installation() {
    log_info "验证安装..."
    
    source venv/bin/activate
    
    # 检查关键包
    python -c "import selenium; print(f'Selenium版本: {selenium.__version__}')" || {
        log_error "Selenium导入失败"
        exit 1
    }
    
    python -c "import webdriver_manager; print('WebDriver Manager导入成功')" || {
        log_error "WebDriver Manager导入失败"
        exit 1
    }
    
    # 测试平台检测
    python -c "from Common.platform_utils import get_platform; print(f'检测到平台: {get_platform().value}')" || {
        log_error "平台工具导入失败"
        exit 1
    }
    
    log_success "安装验证通过"
}

# 显示完成信息
show_completion_info() {
    echo ""
    log_success "=== BlackLake测试框架Mac版安装完成 ==="
    echo ""
    echo "使用方法:"
    echo "  1. 运行测试: ./run_tests_mac.sh"
    echo "  2. 启动Web应用: ./run_web_app_mac.sh"
    echo "  3. 手动激活环境: source venv/bin/activate"
    echo ""
    echo "配置文件位置:"
    echo "  - Mac专用配置: Conf/mac/config_mac.ini"
    echo "  - 依赖包配置: requirements_mac.txt"
    echo ""
    echo "注意事项:"
    echo "  - 首次运行会自动下载ChromeDriver"
    echo "  - 建议安装Allure用于生成测试报告: brew install allure"
    echo "  - 如遇权限问题，请检查系统安全设置"
    echo ""
}

# 主函数
main() {
    echo ""
    log_info "=== BlackLake测试框架Mac版安装程序 ==="
    echo ""
    
    check_mac_system
    check_python
    check_homebrew
    install_chrome
    create_virtual_env
    install_dependencies
    create_mac_config
    create_launch_scripts
    verify_installation
    show_completion_info
}

# 运行主函数
main "$@"
