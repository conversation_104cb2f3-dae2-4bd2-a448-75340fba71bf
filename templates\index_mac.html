<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>BlackLake测试框架 - Mac版</title>
    
    <!-- Mac优化的meta标签 -->
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">
    <meta name="apple-mobile-web-app-title" content="BlackLake">
    <meta name="theme-color" content="#007AFF">
    
    <!-- 预加载关键资源 -->
    <link rel="preconnect" href="https://cdn.jsdelivr.net">
    <link rel="dns-prefetch" href="https://cdn.jsdelivr.net">
    
    <!-- 样式表 -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <link href="{{ url_for('static', filename='css/custom.css') }}" rel="stylesheet">
    
    <style>
        /* Mac专用全局样式 */
        body {
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            min-height: 100vh;
            font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Display', sans-serif;
        }
        
        .mac-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .mac-header {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .mac-logo {
            width: 64px;
            height: 64px;
            background: linear-gradient(135deg, #007AFF 0%, #5856D6 100%);
            border-radius: 16px;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 24px;
            margin-bottom: 16px;
            box-shadow: 0 4px 20px rgba(0, 122, 255, 0.3);
        }
        
        .mac-title {
            font-size: 32px;
            font-weight: 700;
            color: #1D1D1F;
            margin-bottom: 8px;
        }
        
        .mac-subtitle {
            font-size: 18px;
            color: #8E8E93;
            font-weight: 400;
        }
        
        .mac-card {
            background: rgba(255, 255, 255, 0.8);
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 16px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            padding: 24px;
            margin-bottom: 20px;
        }
        
        .mac-button {
            background: #007AFF;
            color: white;
            border: none;
            border-radius: 12px;
            padding: 12px 24px;
            font-size: 16px;
            font-weight: 590;
            cursor: pointer;
            transition: all 0.2s cubic-bezier(0.25, 0.46, 0.45, 0.94);
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }
        
        .mac-button:hover {
            background: #0056CC;
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(0, 122, 255, 0.4);
        }
        
        .mac-button:active {
            transform: translateY(0);
        }
        
        .mac-button.secondary {
            background: rgba(142, 142, 147, 0.1);
            color: #8E8E93;
            border: 1px solid rgba(142, 142, 147, 0.3);
        }
        
        .mac-button.secondary:hover {
            background: rgba(142, 142, 147, 0.2);
            color: #8E8E93;
        }
        
        .mac-status {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 14px;
            font-weight: 590;
        }
        
        .mac-status.online {
            background: rgba(52, 199, 89, 0.1);
            color: #34C759;
            border: 1px solid rgba(52, 199, 89, 0.3);
        }
        
        .mac-status.offline {
            background: rgba(255, 59, 48, 0.1);
            color: #FF3B30;
            border: 1px solid rgba(255, 59, 48, 0.3);
        }
        
        .mac-grid {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: 20px;
            margin-top: 20px;
        }
        
        @media (max-width: 768px) {
            .mac-grid {
                grid-template-columns: 1fr;
            }
        }
        
        .mac-feature-list {
            list-style: none;
            padding: 0;
            margin: 0;
        }
        
        .mac-feature-item {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 12px 0;
            border-bottom: 1px solid rgba(0, 0, 0, 0.05);
        }
        
        .mac-feature-item:last-child {
            border-bottom: none;
        }
        
        .mac-feature-icon {
            width: 32px;
            height: 32px;
            background: rgba(0, 122, 255, 0.1);
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #007AFF;
        }
        
        .mac-feature-text {
            flex: 1;
        }
        
        .mac-feature-title {
            font-weight: 590;
            color: #1D1D1F;
            margin-bottom: 4px;
        }
        
        .mac-feature-desc {
            font-size: 14px;
            color: #8E8E93;
        }
    </style>
</head>
<body class="mac-platform">
    <div class="mac-container">
        <!-- Mac风格头部 -->
        <div class="mac-header">
            <div class="mac-logo">
                <i class="bi bi-play-circle-fill"></i>
            </div>
            <h1 class="mac-title">BlackLake测试框架</h1>
            <p class="mac-subtitle">专为Mac优化的自动化测试平台</p>
        </div>
        
        <!-- 连接状态 -->
        <div class="text-center mb-4">
            <div id="connectionStatus" class="mac-status offline">
                <i class="bi bi-wifi-off"></i>
                <span>连接中...</span>
            </div>
        </div>
        
        <!-- 主要功能区域 -->
        <div class="mac-grid">
            <!-- 左侧：功能介绍 -->
            <div class="mac-card">
                <h3 class="mb-4">
                    <i class="bi bi-star-fill text-warning"></i>
                    Mac优化特性
                </h3>
                <ul class="mac-feature-list">
                    <li class="mac-feature-item">
                        <div class="mac-feature-icon">
                            <i class="bi bi-cpu"></i>
                        </div>
                        <div class="mac-feature-text">
                            <div class="mac-feature-title">智能平台检测</div>
                            <div class="mac-feature-desc">自动识别Mac系统并应用专用配置</div>
                        </div>
                    </li>
                    <li class="mac-feature-item">
                        <div class="mac-feature-icon">
                            <i class="bi bi-palette"></i>
                        </div>
                        <div class="mac-feature-text">
                            <div class="mac-feature-title">Mac原生界面</div>
                            <div class="mac-feature-desc">采用Mac设计语言的现代化界面</div>
                        </div>
                    </li>
                    <li class="mac-feature-item">
                        <div class="mac-feature-icon">
                            <i class="bi bi-keyboard"></i>
                        </div>
                        <div class="mac-feature-text">
                            <div class="mac-feature-title">Mac快捷键支持</div>
                            <div class="mac-feature-desc">Cmd+R刷新, Cmd+A全选, Cmd+Enter执行</div>
                        </div>
                    </li>
                    <li class="mac-feature-item">
                        <div class="mac-feature-icon">
                            <i class="bi bi-gear"></i>
                        </div>
                        <div class="mac-feature-text">
                            <div class="mac-feature-title">自动驱动管理</div>
                            <div class="mac-feature-desc">自动下载和配置Mac版Chrome驱动</div>
                        </div>
                    </li>
                </ul>
            </div>
            
            <!-- 右侧：快速操作 -->
            <div class="mac-card">
                <h3 class="mb-4">
                    <i class="bi bi-lightning-fill text-primary"></i>
                    快速开始
                </h3>
                <div class="d-grid gap-3">
                    <button class="mac-button" onclick="openFullConsole()">
                        <i class="bi bi-play-circle"></i>
                        打开测试控制台
                    </button>
                    <button class="mac-button secondary" onclick="runCompatibilityTest()">
                        <i class="bi bi-check-circle"></i>
                        运行兼容性测试
                    </button>
                    <button class="mac-button secondary" onclick="viewDocumentation()">
                        <i class="bi bi-book"></i>
                        查看使用文档
                    </button>
                </div>
                
                <hr class="my-4">
                
                <h5 class="mb-3">系统信息</h5>
                <div id="systemInfo" class="small text-muted">
                    <div>平台: <span id="platformInfo">检测中...</span></div>
                    <div>浏览器: <span id="browserInfo">检测中...</span></div>
                    <div>分辨率: <span id="screenInfo">检测中...</span></div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.socket.io/4.5.0/socket.io.min.js"></script>
    
    <script>
        // Mac专用JavaScript
        class MacInterface {
            constructor() {
                this.initializeInterface();
                this.detectSystemInfo();
                this.connectWebSocket();
            }
            
            initializeInterface() {
                // 添加Mac特有的交互效果
                document.addEventListener('keydown', (e) => {
                    if (e.metaKey && e.key === 'r') {
                        e.preventDefault();
                        location.reload();
                    }
                });
                
                console.log('🍎 Mac界面初始化完成');
            }
            
            detectSystemInfo() {
                const platformInfo = navigator.platform;
                const browserInfo = navigator.userAgent.split(' ').pop();
                const screenInfo = `${screen.width}x${screen.height}`;
                
                document.getElementById('platformInfo').textContent = platformInfo;
                document.getElementById('browserInfo').textContent = browserInfo;
                document.getElementById('screenInfo').textContent = screenInfo;
            }
            
            connectWebSocket() {
                try {
                    const socket = io();
                    
                    socket.on('connect', () => {
                        const status = document.getElementById('connectionStatus');
                        status.className = 'mac-status online';
                        status.innerHTML = '<i class="bi bi-wifi"></i><span>已连接</span>';
                    });
                    
                    socket.on('disconnect', () => {
                        const status = document.getElementById('connectionStatus');
                        status.className = 'mac-status offline';
                        status.innerHTML = '<i class="bi bi-wifi-off"></i><span>连接断开</span>';
                    });
                } catch (error) {
                    console.log('WebSocket连接失败:', error);
                }
            }
        }
        
        // 快速操作函数
        function openFullConsole() {
            window.location.href = '/';
        }
        
        function runCompatibilityTest() {
            alert('兼容性测试功能开发中...');
        }
        
        function viewDocumentation() {
            window.open('/static/README_MAC.md', '_blank');
        }
        
        // 初始化Mac界面
        document.addEventListener('DOMContentLoaded', () => {
            new MacInterface();
        });
    </script>
</body>
</html>
