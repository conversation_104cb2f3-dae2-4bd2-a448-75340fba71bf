#!/usr/bin/env python3
"""
BlackLake测试框架 - Web应用启动器
跨平台的Web应用启动脚本，自动检测平台并应用相应配置

使用方法:
    python start_web_app.py [--port PORT] [--host HOST] [--debug]

参数:
    --port PORT     指定端口号（默认5000）
    --host HOST     指定主机地址（默认127.0.0.1）
    --debug         启用调试模式
    --simple        使用简化版Web应用

作者: BlackLake Team
版本: 1.0.0
"""

import os
import sys
import argparse
import subprocess
from pathlib import Path

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

try:
    from Common.platform_utils import get_platform, is_mac, get_platform_info
    from Common.my_logger import logger
except ImportError:
    print("警告: 无法导入平台工具，使用基础功能")
    logger = None

def detect_platform():
    """检测当前平台"""
    try:
        return get_platform().value
    except:
        import platform
        system = platform.system().lower()
        if system == 'darwin':
            return 'mac'
        elif system == 'windows':
            return 'windows'
        elif system == 'linux':
            return 'linux'
        else:
            return 'unknown'

def check_dependencies():
    """检查必要的依赖包"""
    required_packages = ['flask', 'flask-socketio']
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package.replace('-', '_'))
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        print(f"❌ 缺少必要的依赖包: {', '.join(missing_packages)}")
        print("请运行以下命令安装:")
        print(f"pip install {' '.join(missing_packages)}")
        return False
    
    return True

def setup_environment(platform):
    """设置环境变量"""
    os.environ['PYTHONPATH'] = project_root
    os.environ['BLACKLAKE_PLATFORM'] = platform
    
    # Mac特定环境变量
    if platform == 'mac':
        os.environ['OBJC_DISABLE_INITIALIZE_FORK_SAFETY'] = 'YES'
    
    print(f"✅ 环境设置完成 - 平台: {platform}")

def start_web_application(app_file, host, port, debug):
    """启动Web应用"""
    print(f"\n🚀 启动BlackLake测试框架Web应用")
    print(f"应用文件: {app_file}")
    print(f"访问地址: http://{host}:{port}")
    
    platform = detect_platform()
    if platform == 'mac':
        print(f"Mac优化界面: http://{host}:{port}/mac")
        print(f"经典界面: http://{host}:{port}/classic")
    
    print(f"平台: {platform}")
    print(f"调试模式: {'开启' if debug else '关闭'}")
    print("\n按 Ctrl+C 停止应用\n")
    
    try:
        # 设置Flask环境变量
        os.environ['FLASK_APP'] = app_file
        if debug:
            os.environ['FLASK_ENV'] = 'development'
            os.environ['FLASK_DEBUG'] = '1'
        
        # 启动应用
        if app_file == 'simple_web_app.py':
            from simple_web_app import app
        else:
            from web_app import app
        
        app.run(
            host=host,
            port=port,
            debug=debug,
            threaded=True,
            use_reloader=False  # 避免重复启动
        )
        
    except KeyboardInterrupt:
        print("\n\n👋 Web应用已停止")
    except Exception as e:
        print(f"\n❌ 启动失败: {str(e)}")
        return False
    
    return True

def open_browser(host, port, platform):
    """打开浏览器"""
    import time
    import webbrowser
    
    time.sleep(2)  # 等待应用启动
    
    base_url = f"http://{host}:{port}"
    
    try:
        if platform == 'mac':
            # Mac用户优先打开Mac优化界面
            mac_url = f"{base_url}/mac"
            webbrowser.open(mac_url)
            print(f"🍎 已在浏览器中打开Mac优化界面: {mac_url}")
        else:
            webbrowser.open(base_url)
            print(f"🌐 已在浏览器中打开: {base_url}")
    except Exception as e:
        print(f"⚠️ 无法自动打开浏览器: {e}")
        print(f"请手动访问: {base_url}")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='BlackLake测试框架Web应用启动器')
    parser.add_argument('--port', type=int, default=5000, help='端口号（默认5000）')
    parser.add_argument('--host', default='127.0.0.1', help='主机地址（默认127.0.0.1）')
    parser.add_argument('--debug', action='store_true', help='启用调试模式')
    parser.add_argument('--simple', action='store_true', help='使用简化版Web应用')
    parser.add_argument('--no-browser', action='store_true', help='不自动打开浏览器')
    
    args = parser.parse_args()
    
    print("🎯 BlackLake测试框架 - Web应用启动器")
    print("=" * 50)
    
    # 检测平台
    platform = detect_platform()
    print(f"检测到平台: {platform}")
    
    # 检查依赖
    if not check_dependencies():
        sys.exit(1)
    
    # 设置环境
    setup_environment(platform)
    
    # 选择应用文件
    app_file = 'simple_web_app.py' if args.simple else 'web_app.py'
    
    # 检查应用文件是否存在
    if not os.path.exists(app_file):
        print(f"❌ 应用文件不存在: {app_file}")
        sys.exit(1)
    
    # 在后台打开浏览器
    if not args.no_browser:
        import threading
        browser_thread = threading.Thread(
            target=open_browser, 
            args=(args.host, args.port, platform)
        )
        browser_thread.daemon = True
        browser_thread.start()
    
    # 启动Web应用
    success = start_web_application(app_file, args.host, args.port, args.debug)
    
    if not success:
        sys.exit(1)

if __name__ == '__main__':
    main()
