#!/usr/bin/env python3
"""
Mac兼容性测试脚本

用于验证BlackLake测试框架在Mac平台上的兼容性。
测试所有跨平台功能是否正常工作。

使用方法:
    python test_mac_compatibility.py

作者: BlackLake Team
版本: 1.0.0
"""

import sys
import os
import traceback
from typing import List, Tuple, Dict, Any

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_platform_detection() -> Tuple[bool, str]:
    """测试平台检测功能"""
    try:
        from Common.platform_utils import (
            get_platform, is_mac, is_windows, is_linux,
            get_platform_info, PlatformType
        )
        
        platform = get_platform()
        platform_info = get_platform_info()
        
        print(f"检测到平台: {platform.value}")
        print(f"是否Mac: {is_mac()}")
        print(f"是否Windows: {is_windows()}")
        print(f"是否Linux: {is_linux()}")
        
        if sys.platform == 'darwin' and not is_mac():
            return False, "Mac平台检测失败"
        
        return True, "平台检测正常"
        
    except Exception as e:
        return False, f"平台检测异常: {str(e)}"


def test_path_handling() -> Tuple[bool, str]:
    """测试路径处理功能"""
    try:
        from Common.handle_path import (
            base_dir, cases_dir, logs_dir, screenshot_dir,
            ensure_dir_exists, get_platform_config_dir,
            get_log_file_path, get_screenshot_path
        )
        
        # 测试基础路径
        paths_to_check = [
            ("base_dir", base_dir),
            ("cases_dir", cases_dir),
            ("logs_dir", logs_dir),
            ("screenshot_dir", screenshot_dir)
        ]
        
        for name, path in paths_to_check:
            if not os.path.exists(path):
                return False, f"路径不存在: {name} = {path}"
            print(f"✓ {name}: {path}")
        
        # 测试路径创建功能
        test_dir = os.path.join(base_dir, "test_temp_dir")
        ensure_dir_exists(test_dir)
        if not os.path.exists(test_dir):
            return False, "目录创建失败"
        
        # 清理测试目录
        os.rmdir(test_dir)
        
        return True, "路径处理正常"
        
    except Exception as e:
        return False, f"路径处理异常: {str(e)}"


def test_keyboard_utils() -> Tuple[bool, str]:
    """测试键盘工具功能"""
    try:
        from Common.platform_utils import KeyboardUtils
        
        select_all = KeyboardUtils.get_select_all_key()
        copy_key = KeyboardUtils.get_copy_key()
        paste_key = KeyboardUtils.get_paste_key()
        selenium_key = KeyboardUtils.get_selenium_modifier_key()
        
        print(f"全选快捷键: {select_all}")
        print(f"复制快捷键: {copy_key}")
        print(f"粘贴快捷键: {paste_key}")
        print(f"Selenium修饰键: {selenium_key}")
        
        # 在Mac上应该是cmd相关的快捷键
        if sys.platform == 'darwin':
            if 'cmd' not in select_all.lower():
                return False, "Mac平台快捷键配置错误"
        
        return True, "键盘工具正常"
        
    except Exception as e:
        return False, f"键盘工具异常: {str(e)}"


def test_driver_manager() -> Tuple[bool, str]:
    """测试驱动管理器功能"""
    try:
        from Common.driver_manager import (
            CrossPlatformDriverManager, get_platform_driver
        )
        
        # 创建驱动管理器实例
        manager = CrossPlatformDriverManager()
        print(f"驱动管理器平台: {manager.platform}")
        
        # 测试Chrome选项获取
        chrome_options = manager.get_chrome_options()
        print(f"Chrome选项配置完成，参数数量: {len(chrome_options.arguments)}")
        
        # 注意：这里不实际创建WebDriver，避免需要Chrome浏览器
        print("驱动管理器基础功能测试通过")
        
        return True, "驱动管理器正常"
        
    except Exception as e:
        return False, f"驱动管理器异常: {str(e)}"


def test_webdriver_creation() -> Tuple[bool, str]:
    """测试WebDriver创建（需要Chrome浏览器）"""
    try:
        from Common.driver_manager import get_platform_driver
        
        # 检查Chrome是否安装
        chrome_paths = [
            "/Applications/Google Chrome.app/Contents/MacOS/Google Chrome",
            "/usr/bin/google-chrome",
            "/usr/bin/chromium-browser"
        ]
        
        chrome_found = any(os.path.exists(path) for path in chrome_paths)
        
        if not chrome_found:
            return True, "Chrome未安装，跳过WebDriver测试"
        
        # 尝试创建WebDriver
        print("尝试创建Chrome WebDriver...")
        driver = get_platform_driver('chrome', headless=True)
        
        # 简单测试
        driver.get("data:text/html,<html><body><h1>Test</h1></body></html>")
        title = driver.title
        
        # 清理
        driver.quit()
        
        return True, f"WebDriver创建成功，页面标题: {title}"
        
    except Exception as e:
        return True, f"WebDriver测试跳过: {str(e)}"


def test_configuration_loading() -> Tuple[bool, str]:
    """测试配置文件加载"""
    try:
        from Common.handle_config import HandleConfig
        from Common.handle_path import conf_dir
        
        # 检查配置文件
        config_files = [
            os.path.join(conf_dir, "config_xgd.ini"),
            os.path.join(conf_dir, "config_mac.ini")
        ]
        
        for config_file in config_files:
            if os.path.exists(config_file):
                print(f"✓ 配置文件存在: {config_file}")
                
                # 尝试加载配置
                try:
                    config = HandleConfig(config_file)
                    print(f"✓ 配置文件加载成功: {config_file}")
                except:
                    print(f"⚠ 配置文件加载失败: {config_file}")
        
        return True, "配置文件处理正常"
        
    except Exception as e:
        return False, f"配置文件处理异常: {str(e)}"


def run_all_tests() -> Dict[str, Tuple[bool, str]]:
    """运行所有测试"""
    tests = [
        ("平台检测", test_platform_detection),
        ("路径处理", test_path_handling),
        ("键盘工具", test_keyboard_utils),
        ("驱动管理器", test_driver_manager),
        ("WebDriver创建", test_webdriver_creation),
        ("配置文件", test_configuration_loading)
    ]
    
    results = {}
    
    print("=" * 60)
    print("BlackLake框架 Mac兼容性测试")
    print("=" * 60)
    
    for test_name, test_func in tests:
        print(f"\n🧪 测试: {test_name}")
        print("-" * 40)
        
        try:
            success, message = test_func()
            results[test_name] = (success, message)
            
            status = "✅ 通过" if success else "❌ 失败"
            print(f"{status}: {message}")
            
        except Exception as e:
            error_msg = f"测试异常: {str(e)}"
            results[test_name] = (False, error_msg)
            print(f"❌ 异常: {error_msg}")
            print(f"详细错误:\n{traceback.format_exc()}")
    
    return results


def print_summary(results: Dict[str, Tuple[bool, str]]):
    """打印测试总结"""
    print("\n" + "=" * 60)
    print("测试总结")
    print("=" * 60)
    
    passed = sum(1 for success, _ in results.values() if success)
    total = len(results)
    
    print(f"总测试数: {total}")
    print(f"通过数: {passed}")
    print(f"失败数: {total - passed}")
    print(f"通过率: {passed/total*100:.1f}%")
    
    print("\n详细结果:")
    for test_name, (success, message) in results.items():
        status = "✅" if success else "❌"
        print(f"  {status} {test_name}: {message}")
    
    if passed == total:
        print("\n🎉 所有测试通过！框架已准备好在Mac上运行。")
    else:
        print(f"\n⚠️  有 {total - passed} 个测试失败，请检查相关功能。")


if __name__ == "__main__":
    print(f"Python版本: {sys.version}")
    print(f"运行平台: {sys.platform}")
    print(f"当前目录: {os.getcwd()}")
    
    results = run_all_tests()
    print_summary(results)
    
    # 返回适当的退出码
    failed_count = sum(1 for success, _ in results.values() if not success)
    sys.exit(failed_count)
