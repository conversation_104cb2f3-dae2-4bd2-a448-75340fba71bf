from selenium.webdriver.common.by import By

# 创建仓库
class WareHousePageLocs:
    # 仓库菜单
    warehouse_loc = (By.CSS_SELECTOR, "a[href*='warehouse']")

    # 创建仓库
    create_warehouse_loc = (By.XPATH, "//span[contains(text(), '创建仓库')]")

    # 入框仓库编码
    warehouse_code_loc = (By.XPATH,"//*[@id='code']")

    # 入框仓库名称
    warehouse_name_loc = (By.XPATH, "//*[@id='name']")

    # 备注输入框
    warehouse_remark_loc = (By.XPATH, "//*[@id='remark' and @rows]")

    # 创建仓库确定按钮
    warehouse_submit_loc = (By.CSS_SELECTOR, "button.kodo-new-btn-primary:nth-of-type(2)")

    # 创建仓库成功提示
    create_tip_loc = (By.XPATH, "//span[contains(text(), '创建成功')]")

    # 编辑仓库按钮
    update_warehouse_loc = (By.XPATH, "//div[contains(text(), '编辑')]")

    # 编辑仓库成功按钮
    update_warehouse_success_loc = (By.XPATH, "//span[contains(text(), '保存成功')]")

    # 删除仓库按钮
    delete_warehouse_loc = (By.XPATH, "//div[contains(text(), '删除')]")

    # 确认删除仓库按钮
    sure_delete_warehouse_loc = (By.XPATH, "//span[contains(text(), '删 除')]")

    # 删除仓库成功
    delete_warehouse_success_loc = (By.XPATH, "//span[contains(text(), '删除成功')]")

    # 车间看板
    workshopKanBan_loc = (By.XPATH, "//span[contains(text(), '车间看板')]")








