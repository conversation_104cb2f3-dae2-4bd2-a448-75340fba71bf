<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>BlackLake测试框架 - Mac版控制台</title>

    <!-- Mac优化的meta标签 -->
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">
    <meta name="apple-mobile-web-app-title" content="BlackLake">

    <!-- 预加载关键资源 -->
    <link rel="preconnect" href="https://cdn.jsdelivr.net">
    <link rel="dns-prefetch" href="https://cdn.jsdelivr.net">

    <!-- 样式表 -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet" integrity="sha384-9ndCyUaIbzAi2FUVXJi0CjmCapSmO7SnpJef0486qhLnuZ2cdeRhO02iuK6FUUVM" crossorigin="anonymous">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <link href="{{ url_for('static', filename='css/custom.css') }}" rel="stylesheet">
    <style>
        /* Mac专用内联样式 */
        .mac-window {
            background: rgba(255, 255, 255, 0.8);
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 12px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }

        .mac-title-bar {
            height: 28px;
            background: rgba(246, 246, 246, 0.8);
            border-radius: 12px 12px 0 0;
            display: flex;
            align-items: center;
            padding: 0 16px;
            border-bottom: 1px solid rgba(0, 0, 0, 0.1);
        }

        .mac-traffic-lights {
            display: flex;
            gap: 8px;
        }

        .mac-traffic-light {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            border: 0.5px solid rgba(0, 0, 0, 0.1);
        }

        .mac-traffic-light.close { background: #FF5F57; }
        .mac-traffic-light.minimize { background: #FFBD2E; }
        .mac-traffic-light.maximize { background: #28CA42; }

        .mac-window-title {
            flex: 1;
            text-align: center;
            font-size: 13px;
            font-weight: 590;
            color: #1D1D1F;
            margin: 0;
        }

        /* Mac风格的测试用例项 */
        .test-case-item {
            background: rgba(255, 255, 255, 0.7);
            border: 1px solid rgba(0, 0, 0, 0.06);
            border-radius: 8px;
            margin-bottom: 8px;
            padding: 12px 16px;
            transition: all 0.2s cubic-bezier(0.25, 0.46, 0.45, 0.94);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
        }

        .test-case-item:hover {
            background: rgba(255, 255, 255, 0.9);
            border-color: rgba(0, 122, 255, 0.3);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            transform: translateY(-1px);
        }

        .test-case-item.selected {
            background: rgba(0, 122, 255, 0.1);
            border-color: #007AFF;
            box-shadow: 0 0 0 3px rgba(0, 122, 255, 0.1);
        }

        /* Mac风格的状态徽章 */
        .status-badge {
            font-size: 11px;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 590;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        /* Mac风格的连接状态 */
        .connection-status {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1000;
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
            border-radius: 20px;
            padding: 8px 12px;
            background: rgba(255, 255, 255, 0.8);
            border: 1px solid rgba(0, 0, 0, 0.1);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        /* Mac风格的加载动画 */
        .mac-spinner {
            width: 20px;
            height: 20px;
            border: 2px solid rgba(0, 122, 255, 0.2);
            border-top: 2px solid #007AFF;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* Mac风格的滚动区域 */
        .mac-scroll-area {
            max-height: 60vh;
            overflow-y: auto;
            padding-right: 8px;
        }

        .mac-scroll-area::-webkit-scrollbar {
            width: 6px;
        }

        .mac-scroll-area::-webkit-scrollbar-track {
            background: transparent;
        }

        .mac-scroll-area::-webkit-scrollbar-thumb {
            background: rgba(0, 0, 0, 0.2);
            border-radius: 3px;
        }

        .mac-scroll-area::-webkit-scrollbar-thumb:hover {
            background: rgba(0, 0, 0, 0.3);
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <!-- 连接状态指示器 -->
        <div class="connection-status">
            <span id="connectionStatus" class="badge bg-secondary">
                <i class="bi bi-wifi-off"></i> 连接中...
            </span>
        </div>
        
        <!-- Mac风格页面标题 -->
        <div class="row mt-3">
            <div class="col-12">
                <div class="mac-window">
                    <div class="mac-title-bar">
                        <div class="mac-traffic-lights">
                            <div class="mac-traffic-light close"></div>
                            <div class="mac-traffic-light minimize"></div>
                            <div class="mac-traffic-light maximize"></div>
                        </div>
                        <h6 class="mac-window-title">BlackLake测试框架 - Mac版</h6>
                    </div>
                    <div class="page-title">
                        <h1 class="mb-0">
                            <i class="bi bi-play-circle"></i> 测试用例执行控制台
                        </h1>
                        <p class="mb-0 mt-2 opacity-75">专为Mac优化的自动化测试管理平台</p>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row">
            <!-- 左侧：测试用例列表 - Mac风格 -->
            <div class="col-md-8">
                <div class="card mac-window">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">
                            <i class="bi bi-list-check"></i> 测试用例列表
                        </h5>
                        <div class="btn-group" role="group">
                            <button id="refreshBtn" class="btn btn-outline-primary btn-sm">
                                <i class="bi bi-arrow-clockwise"></i> 刷新
                            </button>
                            <button id="selectAllBtn" class="btn btn-outline-secondary btn-sm">
                                <i class="bi bi-check-all"></i> 全选
                            </button>
                            <button id="clearSelectionBtn" class="btn btn-outline-secondary btn-sm">
                                <i class="bi bi-x-circle"></i> 清除
                            </button>
                        </div>
                    </div>
                    <div class="card-body">
                        <div id="loadingSpinner" class="text-center py-4">
                            <div class="mac-spinner"></div>
                            <p class="mt-3 text-muted">正在加载测试用例...</p>
                        </div>
                        <div id="testCasesList" class="mac-scroll-area" style="display: none;">
                            <!-- 测试用例将在这里动态加载 -->
                        </div>
                        <div id="errorMessage" class="alert alert-danger" style="display: none;">
                            <!-- 错误信息将在这里显示 -->
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 右侧：执行控制面板 -->
            <div class="col-md-4">
                <div class="execution-panel">
                    <h5 class="mb-3">
                        <i class="bi bi-gear"></i> 执行控制
                    </h5>
                    
                    <!-- 选择的测试用例信息 -->
                    <div class="mb-3">
                        <label class="form-label">已选择的测试用例:</label>
                        <div id="selectedTestsInfo" class="text-muted">
                            <small>请选择要执行的测试用例</small>
                        </div>
                    </div>
                    
                    <!-- 执行按钮 -->
                    <div class="d-grid gap-2 mb-3">
                        <button id="executeSelectedBtn" class="btn btn-primary" disabled>
                            <i class="bi bi-play-fill"></i> 执行选中的测试
                        </button>
                        <button id="executeAllBtn" class="btn btn-success">
                            <i class="bi bi-play-circle"></i> 执行所有测试
                        </button>
                        <button id="stopExecutionBtn" class="btn btn-danger" disabled>
                            <i class="bi bi-stop-fill"></i> 停止执行
                        </button>
                    </div>
                    
                    <!-- 执行状态 -->
                    <div id="executionStatus" class="mb-3" style="display: none;">
                        <h6>执行状态</h6>
                        <div class="progress-container">
                            <div class="progress">
                                <div id="progressBar" class="progress-bar" role="progressbar" 
                                     style="width: 0%" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100">
                                </div>
                            </div>
                            <small id="progressText" class="text-muted">准备中...</small>
                        </div>
                        <div id="currentTestInfo" class="text-info">
                            <!-- 当前执行的测试信息 -->
                        </div>
                    </div>
                    
                    <!-- 执行结果摘要 -->
                    <div id="resultsSummary" class="mb-3" style="display: none;">
                        <h6>执行结果</h6>
                        <div id="summaryContent">
                            <!-- 结果摘要将在这里显示 -->
                        </div>
                    </div>
                    
                    <!-- 快速操作 -->
                    <div class="mb-3">
                        <h6>快速操作</h6>
                        <div class="d-grid gap-1">
                            <button id="viewReportsBtn" class="btn btn-outline-info btn-sm">
                                <i class="bi bi-file-earmark-text"></i> 查看测试报告
                            </button>
                            <button id="clearLogsBtn" class="btn btn-outline-warning btn-sm">
                                <i class="bi bi-trash"></i> 清除日志
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 执行日志区域 -->
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="bi bi-terminal"></i> 执行日志
                        </h5>
                    </div>
                    <div class="card-body">
                        <div id="executionLogs" class="log-output">
                            欢迎使用测试用例执行控制台！请选择要执行的测试用例。
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Socket.IO -->
    <script src="https://cdn.socket.io/4.5.0/socket.io.min.js"></script>
    <!-- 自定义JavaScript -->
    <script src="{{ url_for('static', filename='js/app.js') }}"></script>
</body>
</html>
