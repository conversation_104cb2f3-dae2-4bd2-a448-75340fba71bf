#!/bin/bash

# BlackLake测试框架 - Mac测试运行脚本
# 版本: 1.0.0

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查虚拟环境
check_virtual_env() {
    if [ ! -d "venv" ]; then
        log_error "虚拟环境不存在！请先运行 ./setup_mac.sh"
        exit 1
    fi
    
    log_info "激活虚拟环境..."
    source venv/bin/activate
    
    # 验证Python环境
    python --version
    pip --version
}

# 设置环境变量
setup_environment() {
    log_info "设置环境变量..."
    
    export PYTHONPATH="${PYTHONPATH}:$(pwd)"
    export BLACKLAKE_PLATFORM=mac
    export BLACKLAKE_LOG_LEVEL=INFO
    
    # Mac特定环境变量
    export OBJC_DISABLE_INITIALIZE_FORK_SAFETY=YES  # 解决Mac上的fork问题
    
    log_success "环境变量设置完成"
}

# 运行兼容性测试
run_compatibility_test() {
    log_info "运行Mac兼容性测试..."
    
    if [ -f "test_mac_compatibility.py" ]; then
        python test_mac_compatibility.py
        if [ $? -eq 0 ]; then
            log_success "兼容性测试通过"
        else
            log_warning "兼容性测试有警告，但继续运行"
        fi
    else
        log_warning "兼容性测试脚本不存在，跳过"
    fi
}

# 运行测试用例
run_tests() {
    log_info "开始运行测试用例..."
    
    # 创建结果目录
    mkdir -p allure-results
    
    # 运行pytest
    if [ "$1" = "headless" ]; then
        log_info "运行无头模式测试..."
        export BLACKLAKE_HEADLESS=true
    fi
    
    # 根据参数选择测试范围
    if [ -n "$2" ]; then
        log_info "运行指定测试: $2"
        python -m pytest "TestCases/$2" --alluredir=./allure-results -v -s
    else
        log_info "运行所有测试用例..."
        python -m pytest TestCases/ --alluredir=./allure-results -v
    fi
    
    test_exit_code=$?
    
    if [ $test_exit_code -eq 0 ]; then
        log_success "测试运行完成"
    else
        log_error "测试运行失败，退出码: $test_exit_code"
    fi
    
    return $test_exit_code
}

# 生成测试报告
generate_report() {
    log_info "生成测试报告..."
    
    if command -v allure &> /dev/null; then
        log_info "使用Allure生成报告..."
        allure generate ./allure-results -o ./allure-report --clean
        
        if [ $? -eq 0 ]; then
            log_success "Allure报告生成完成: ./allure-report"
            
            # 询问是否打开报告
            read -p "是否打开测试报告? (y/n): " -n 1 -r
            echo
            if [[ $REPLY =~ ^[Yy]$ ]]; then
                allure open ./allure-report
            fi
        else
            log_error "Allure报告生成失败"
        fi
    else
        log_warning "Allure未安装，跳过报告生成"
        log_info "可通过以下命令安装: brew install allure"
    fi
}

# 清理函数
cleanup() {
    log_info "清理临时文件..."
    
    # 清理pytest缓存
    find . -name "__pycache__" -type d -exec rm -rf {} + 2>/dev/null || true
    find . -name "*.pyc" -delete 2>/dev/null || true
    
    # 清理旧的截图（保留最近7天）
    if [ -d "Outputs/screenshots" ]; then
        find Outputs/screenshots -name "*.png" -mtime +7 -delete 2>/dev/null || true
    fi
    
    log_success "清理完成"
}

# 显示帮助信息
show_help() {
    echo "BlackLake测试框架 - Mac测试运行脚本"
    echo ""
    echo "使用方法:"
    echo "  $0 [选项] [测试文件]"
    echo ""
    echo "选项:"
    echo "  headless    使用无头模式运行测试"
    echo "  --help      显示此帮助信息"
    echo "  --clean     运行前清理环境"
    echo "  --compat    只运行兼容性测试"
    echo ""
    echo "示例:"
    echo "  $0                          # 运行所有测试"
    echo "  $0 headless                 # 无头模式运行所有测试"
    echo "  $0 test_login.py           # 运行指定测试文件"
    echo "  $0 headless test_login.py  # 无头模式运行指定测试"
    echo "  $0 --compat               # 只运行兼容性测试"
    echo ""
}

# 主函数
main() {
    echo ""
    log_info "=== BlackLake测试框架 Mac版测试运行器 ==="
    echo ""
    
    # 处理命令行参数
    case "$1" in
        --help|-h)
            show_help
            exit 0
            ;;
        --clean)
            cleanup
            shift
            ;;
        --compat)
            check_virtual_env
            setup_environment
            run_compatibility_test
            exit $?
            ;;
    esac
    
    # 检查环境
    check_virtual_env
    setup_environment
    
    # 运行兼容性测试
    run_compatibility_test
    
    # 运行测试用例
    run_tests "$@"
    test_result=$?
    
    # 生成报告
    if [ $test_result -eq 0 ]; then
        generate_report
    fi
    
    # 清理
    cleanup
    
    # 显示结果
    echo ""
    if [ $test_result -eq 0 ]; then
        log_success "=== 测试运行成功完成 ==="
    else
        log_error "=== 测试运行失败 ==="
    fi
    echo ""
    
    exit $test_result
}

# 运行主函数
main "$@"
