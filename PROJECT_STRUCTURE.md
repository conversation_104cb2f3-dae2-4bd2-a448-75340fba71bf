# BlackLake测试框架 - 项目结构说明

## 📁 项目目录结构

```
BlackLake/
├── 📁 Common/                          # 公共模块目录
│   ├── __init__.py
│   ├── basepage.py                     # 基础页面类（已优化跨平台）
│   ├── driver_manager.py               # 🆕 跨平台驱动管理器
│   ├── handle_config.py                # 配置文件处理
│   ├── handle_path.py                  # 路径处理（已增强）
│   ├── my_logger.py                    # 日志处理
│   └── platform_utils.py               # 🆕 平台工具模块
│
├── 📁 Conf/                            # 配置文件目录
│   ├── README.txt
│   ├── chromedriver.exe                # Windows驱动（保留兼容）
│   ├── config_xgd.ini                  # 通用配置
│   └── config_mac.ini                  # 🆕 Mac专用配置
│
├── 📁 Outputs/                         # 输出文件目录
│   ├── 📁 logs/                        # 日志文件
│   ├── 📁 reports/                     # 测试报告
│   ├── 📁 screenshots/                 # 截图文件
│   ├── 📁 sessions/                    # 会话文件
│   └── 📁 downloads/                   # 下载文件
│
├── 📁 PageLocators/                    # 页面定位器
│   ├── __init__.py
│   ├── base_data_customer_locs.py
│   ├── base_data_equipment_locs.py
│   ├── create_ticket_locs.py
│   ├── defective_products_locs.py
│   ├── home_page_locs.py
│   ├── knowledge_management_locs.py
│   ├── login_page_locs.py
│   ├── pro_management_productionplan_locs.py
│   ├── pro_management_reportwork_locs.py
│   ├── pro_management_workorder_locs.py
│   ├── process_routing_locs.py
│   ├── unit_page_locs.py
│   └── warehouse_page_locs.py
│
├── 📁 PageObjects/                     # 页面对象
│   ├── __init__.py
│   ├── base_data_customer_page.py
│   ├── base_data_equipment_page.py
│   ├── defective_products_page.py
│   ├── home_page.py
│   ├── knowledge_management_page.py
│   ├── login_page.py
│   ├── pro_management_productionplan_page.py
│   ├── pro_management_reportwork_page.py
│   ├── pro_management_workorder_page.py
│   ├── process_routing_page.py
│   ├── ticket_page.py
│   ├── unit_page.py
│   └── warehouse_page.py
│
├── 📁 TestCases/                       # 测试用例目录
│   ├── __init__.py
│   ├── conftest.py                     # 测试配置（已优化）
│   ├── atest_base_data_equipment.py
│   ├── atest_creat_ticket.py
│   ├── atest_creat_unit.py
│   ├── atest_customer.py
│   ├── atest_defects_product.py
│   ├── atest_pro_management_workorder.py
│   ├── atest_process_routing.py
│   ├── atest_scrolllocs.py
│   ├── test_knowledge_management.py
│   ├── test_pro_management_reportwork.py
│   ├── test_production_plan.py
│   ├── test_su_login.py
│   └── test_warehouse.py
│
├── 📁 TestDatas/                       # 测试数据目录
│   ├── __init__.py
│   ├── base_dates.py
│   ├── global_datas.py
│   ├── home_datas.py
│   └── xgd_login_datas.py
│
├── 📁 static/                          # 静态文件（Web界面）
│   ├── 📁 css/
│   └── 📁 js/
│
├── 📁 templates/                       # 模板文件（Web界面）
│   ├── index.html
│   ├── sessions.html
│   └── simple_index.html
│
├── 📁 temp/                            # 临时文件目录
├── 📁 venv/                            # Python虚拟环境
│
├── 📄 brand_config.py                  # 品牌配置
├── 📄 check_brand_update.py            # 品牌更新检查
├── 📄 chromedriver.exe                 # Windows Chrome驱动
├── 📄 main.py                          # 主程序入口
├── 📄 pytest_parser.py                # Pytest解析器
├── 📄 session_manager.py               # 会话管理器
├── 📄 simple_web_app.py                # 简化Web应用
├── 📄 web_app.py                       # 完整Web应用
│
├── 📄 requirements_simple.txt          # 简化版依赖（已更新）
├── 📄 requirements_web.txt             # Web版依赖（已更新）
├── 📄 requirements_mac.txt             # 🆕 Mac专用依赖
│
├── 📄 setup_mac.sh                     # 🆕 Mac自动安装脚本
├── 📄 run_tests_mac.sh                 # 🆕 Mac测试运行脚本
├── 📄 run_web_app_mac.sh               # 🆕 Mac Web应用启动脚本
├── 📄 test_mac_compatibility.py        # 🆕 Mac兼容性测试
│
├── 📄 README_MAC.md                    # 🆕 Mac使用说明
├── 📄 README_CROSSPLATFORM.md          # 🆕 跨平台使用指南
├── 📄 MAC_OPTIMIZATION_SUMMARY.md      # 🆕 Mac优化总结
└── 📄 PROJECT_STRUCTURE.md             # 🆕 项目结构说明（本文件）
```

## 🆕 新增文件说明

### 核心模块
- **`Common/driver_manager.py`** - 跨平台WebDriver管理器，自动检测平台并配置相应驱动
- **`Common/platform_utils.py`** - 平台检测和工具函数，提供跨平台兼容性支持

### 配置文件
- **`Conf/config_mac.ini`** - Mac平台专用配置，包含Mac特定的浏览器选项和路径设置
- **`requirements_mac.txt`** - Mac平台优化的Python依赖包配置

### 安装和运行脚本
- **`setup_mac.sh`** - Mac平台一键安装脚本，自动处理所有依赖和环境配置
- **`run_tests_mac.sh`** - Mac平台测试运行脚本，包含环境检查和报告生成
- **`run_web_app_mac.sh`** - Mac平台Web应用启动脚本，自动处理端口和浏览器

### 测试和验证
- **`test_mac_compatibility.py`** - 全面的Mac兼容性测试脚本，验证所有跨平台功能

### 文档
- **`README_MAC.md`** - Mac平台详细使用说明
- **`README_CROSSPLATFORM.md`** - 跨平台使用指南
- **`MAC_OPTIMIZATION_SUMMARY.md`** - Mac优化工作总结

## 🔧 修改的文件

### 核心功能增强
- **`Common/basepage.py`** - 增加跨平台键盘快捷键支持
- **`Common/handle_path.py`** - 增强路径处理功能，添加平台特定路径支持
- **`TestCases/conftest.py`** - 优化WebDriver配置，使用跨平台驱动管理器

### 依赖配置更新
- **`requirements_simple.txt`** - 添加selenium和webdriver-manager
- **`requirements_web.txt`** - 添加webdriver-manager支持

## 🎯 关键特性

### 1. 自动平台检测
```python
from Common.platform_utils import get_platform, is_mac
platform = get_platform()  # 自动检测当前平台
```

### 2. 智能驱动管理
```python
from Common.driver_manager import get_platform_driver
driver = get_platform_driver('chrome')  # 自动配置适合当前平台的驱动
```

### 3. 跨平台快捷键
```python
from Common.platform_utils import KeyboardUtils
modifier = KeyboardUtils.get_selenium_modifier_key()  # Mac: Cmd, Windows: Ctrl
```

### 4. 平台特定配置
- Mac: `Conf/config_mac.ini`
- Windows: `Conf/config_xgd.ini`
- 自动选择适合当前平台的配置文件

## 🚀 使用方式

### Mac用户
```bash
# 一键安装
./setup_mac.sh

# 运行测试
./run_tests_mac.sh

# 启动Web应用
./run_web_app_mac.sh
```

### Windows用户
```bash
# 保持原有使用方式
python main.py
python web_app.py
```

### 跨平台通用
```bash
# 兼容性测试
python test_mac_compatibility.py

# 通用测试运行
python -m pytest TestCases/ --alluredir=./allure-results -v
```

## 📊 兼容性矩阵

| 功能 | Windows | Mac | Linux |
|------|---------|-----|-------|
| 基础测试 | ✅ | ✅ | 🔄 |
| WebDriver | ✅ | ✅ | 🔄 |
| Web界面 | ✅ | ✅ | 🔄 |
| 报告生成 | ✅ | ✅ | 🔄 |
| 自动安装 | ✅ | ✅ | 🔄 |

**图例:**
- ✅ 完全支持
- 🔄 计划支持
- ❌ 不支持

---

**项目版本**: v1.0.0 (跨平台版)  
**更新时间**: 2025-09-04  
**BlackLake Team** | 一次编写，处处运行 🌍
