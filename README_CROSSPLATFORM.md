# BlackLake测试框架 - 跨平台版本

## 🌟 概述

BlackLake测试框架现已全面支持跨平台运行！无论您使用Windows、Mac还是Linux，都能享受一致的自动化测试体验。

## 🚀 支持的平台

| 平台 | 状态 | 安装方式 | 备注 |
|------|------|----------|------|
| **Windows** | ✅ 完全支持 | `setup_windows.bat` | 原生支持 |
| **macOS** | ✅ 完全支持 | `setup_mac.sh` | 新增支持 |
| **Linux** | ✅ 基础支持 | `setup_linux.sh` | 计划中 |

## 📋 系统要求

### 通用要求
- **Python**: 3.8+ 
- **内存**: 4GB+ RAM
- **存储**: 2GB+ 可用空间
- **网络**: 稳定的互联网连接（用于下载驱动）

### 平台特定要求

#### Windows
- Windows 10/11
- PowerShell 5.0+
- Visual C++ Redistributable

#### macOS  
- macOS 10.14+
- Xcode Command Line Tools
- Homebrew（推荐）

#### Linux
- Ubuntu 18.04+ / CentOS 7+
- apt/yum 包管理器
- X11 显示服务器

## 🛠️ 快速安装

### 自动安装（推荐）

根据您的操作系统选择对应的安装脚本：

```bash
# Windows (PowerShell管理员模式)
.\setup_windows.bat

# macOS (Terminal)
chmod +x setup_mac.sh && ./setup_mac.sh

# Linux (Terminal)  
chmod +x setup_linux.sh && ./setup_linux.sh
```

### 手动安装

1. **克隆项目**
   ```bash
   git clone <repository-url>
   cd BlackLake
   ```

2. **创建虚拟环境**
   ```bash
   # Windows
   python -m venv venv
   venv\Scripts\activate
   
   # macOS/Linux
   python3 -m venv venv
   source venv/bin/activate
   ```

3. **安装依赖**
   ```bash
   # 选择对应平台的requirements文件
   pip install -r requirements_web.txt      # 通用版本
   pip install -r requirements_mac.txt      # Mac优化版本
   pip install -r requirements_windows.txt  # Windows优化版本
   ```

## 🎯 核心特性

### 🔄 自动平台检测
```python
from Common.platform_utils import get_platform, is_mac, is_windows

platform = get_platform()  # 返回: PlatformType.MAC/WINDOWS/LINUX
if is_mac():
    print("运行在Mac平台")
```

### 🚗 智能驱动管理
```python
from Common.driver_manager import get_platform_driver

# 自动下载并配置适合当前平台的驱动
driver = get_platform_driver('chrome')
```

### ⌨️ 跨平台快捷键
```python
from Common.platform_utils import KeyboardUtils

# 自动适配平台快捷键: Mac(Cmd+A) / Windows(Ctrl+A)
modifier_key = KeyboardUtils.get_selenium_modifier_key()
```

### 📁 智能路径处理
```python
from Common.platform_utils import PlatformUtils

# 跨平台路径处理
path = PlatformUtils.join_path("folder", "subfolder", "file.txt")
normalized = PlatformUtils.normalize_path(path)
```

## 🏃‍♂️ 使用方法

### 运行测试

```bash
# Windows
run_tests.bat

# macOS
./run_tests_mac.sh

# Linux  
./run_tests_linux.sh

# 通用方式
python -m pytest TestCases/ --alluredir=./allure-results -v
```

### 启动Web界面

```bash
# Windows
run_web_app.bat

# macOS
./run_web_app_mac.sh

# Linux
./run_web_app_linux.sh

# 通用方式
python web_app.py
```

## ⚙️ 配置管理

### 平台特定配置

每个平台都有专用的配置文件：

```
Conf/
├── config_xgd.ini      # 通用配置
├── config_mac.ini      # Mac专用配置
├── config_windows.ini  # Windows专用配置
└── config_linux.ini    # Linux专用配置
```

### 环境变量配置

```bash
# 通用环境变量
export BLACKLAKE_BROWSER=chrome
export BLACKLAKE_HEADLESS=false
export BLACKLAKE_LOG_LEVEL=INFO

# 平台特定环境变量
export BLACKLAKE_PLATFORM_CONFIG=auto  # 自动检测
export BLACKLAKE_DRIVER_AUTO_DOWNLOAD=true
```

## 🔧 高级功能

### 多浏览器支持

```python
from Common.driver_manager import get_platform_driver

# 支持多种浏览器
chrome_driver = get_platform_driver('chrome')
firefox_driver = get_platform_driver('firefox')
# edge_driver = get_platform_driver('edge')  # 计划支持
```

### 并发测试

```bash
# 使用pytest-xdist进行并发测试
pip install pytest-xdist

# 并发运行（根据CPU核心数调整）
python -m pytest TestCases/ -n auto --alluredir=./allure-results
```

### 无头模式

```python
# 启用无头模式（适合CI/CD）
driver = get_platform_driver('chrome', headless=True)
```

## 📊 测试报告

### Allure报告

```bash
# 安装Allure
# Windows: choco install allure
# macOS: brew install allure  
# Linux: 参考官方文档

# 生成报告
allure generate ./allure-results -o ./allure-report --clean
allure open ./allure-report
```

### 自定义报告

框架支持多种报告格式：
- HTML报告
- JSON报告  
- JUnit XML报告
- 自定义报告模板

## 🐛 故障排除

### 常见问题

1. **驱动下载失败**
   ```bash
   # 设置代理
   export HTTP_PROXY=http://proxy:port
   export HTTPS_PROXY=http://proxy:port
   ```

2. **权限问题**
   ```bash
   # Windows: 以管理员身份运行
   # macOS/Linux: 检查文件权限
   chmod +x *.sh
   ```

3. **浏览器启动失败**
   ```bash
   # 检查浏览器安装
   # Windows: 检查注册表
   # macOS: 检查Applications目录
   # Linux: 检查PATH环境变量
   ```

### 调试模式

```bash
# 启用详细日志
export BLACKLAKE_LOG_LEVEL=DEBUG

# 运行单个测试用例
python -m pytest TestCases/test_example.py::test_function -v -s
```

## 🤝 贡献指南

### 开发环境

```bash
# 1. Fork项目
# 2. 克隆到本地
git clone https://github.com/your-username/BlackLake.git

# 3. 创建开发分支
git checkout -b feature/your-feature

# 4. 安装开发依赖
pip install -r requirements_dev.txt

# 5. 运行测试
python -m pytest tests/
```

### 代码规范

- 遵循PEP 8
- 添加类型注解
- 编写单元测试
- 更新文档

## 📈 路线图

### 即将推出
- [ ] Linux完整支持
- [ ] Edge浏览器支持
- [ ] Docker容器化
- [ ] CI/CD模板
- [ ] 性能监控
- [ ] 移动端支持

### 长期计划
- [ ] 云端测试支持
- [ ] AI辅助测试
- [ ] 可视化测试编辑器
- [ ] 多语言SDK

## 📞 技术支持

- **文档**: 查看对应平台的README文件
- **日志**: 检查`Outputs/logs/`目录
- **社区**: 提交Issue或Pull Request
- **邮件**: <EMAIL>

---

**BlackLake Team** | 一次编写，处处运行 🌍
