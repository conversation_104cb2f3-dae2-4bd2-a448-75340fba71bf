/**
 * BlackLake测试框架 - Mac优化版前端控制器
 * 专为Mac平台优化的测试用例执行控制台
 */

class MacTestExecutionController {
    constructor() {
        this.socket = null;
        this.testCases = [];
        this.selectedTests = new Set();
        this.isExecuting = false;
        this.platform = this.detectPlatform();

        this.initializeMacFeatures();
        this.initializeSocket();
        this.bindEvents();
        this.loadTestCases();
    }

    /**
     * 检测运行平台
     */
    detectPlatform() {
        const userAgent = navigator.userAgent.toLowerCase();
        const platform = navigator.platform.toLowerCase();

        if (platform.includes('mac') || userAgent.includes('mac')) {
            return 'mac';
        } else if (platform.includes('win') || userAgent.includes('win')) {
            return 'windows';
        } else if (platform.includes('linux') || userAgent.includes('linux')) {
            return 'linux';
        }
        return 'unknown';
    }

    /**
     * 初始化Mac特有功能
     */
    initializeMacFeatures() {
        if (this.platform === 'mac') {
            // 启用Mac风格的滚动
            document.body.style.webkitOverflowScrolling = 'touch';

            // 添加Mac特有的CSS类
            document.body.classList.add('mac-platform');

            // 优化触控板滚动
            this.optimizeScrolling();

            // 添加Mac键盘快捷键支持
            this.addMacKeyboardShortcuts();

            console.log('🍎 Mac平台优化已启用');
        }
    }

    /**
     * 优化Mac滚动体验
     */
    optimizeScrolling() {
        const scrollAreas = document.querySelectorAll('.mac-scroll-area');
        scrollAreas.forEach(area => {
            area.style.webkitOverflowScrolling = 'touch';
            area.style.scrollBehavior = 'smooth';
        });
    }

    /**
     * 添加Mac键盘快捷键
     */
    addMacKeyboardShortcuts() {
        document.addEventListener('keydown', (e) => {
            // Cmd+R: 刷新测试用例
            if (e.metaKey && e.key === 'r') {
                e.preventDefault();
                this.loadTestCases();
                this.showMacNotification('测试用例已刷新', 'info');
            }

            // Cmd+A: 全选测试用例
            if (e.metaKey && e.key === 'a' && e.target.closest('.test-cases-container')) {
                e.preventDefault();
                this.selectAllTests();
                this.showMacNotification('已选择所有测试用例', 'success');
            }

            // Cmd+Enter: 执行选中的测试
            if (e.metaKey && e.key === 'Enter') {
                e.preventDefault();
                if (this.selectedTests.size > 0) {
                    this.executeSelectedTests();
                }
            }

            // Esc: 停止执行
            if (e.key === 'Escape' && this.isExecuting) {
                e.preventDefault();
                this.stopExecution();
            }
        });
    }

    /**
     * 显示Mac风格通知
     */
    showMacNotification(message, type = 'info') {
        const notification = document.createElement('div');
        notification.className = `mac-notification mac-notification-${type}`;
        notification.innerHTML = `
            <div class="mac-notification-content">
                <i class="bi bi-${this.getNotificationIcon(type)}"></i>
                <span>${message}</span>
            </div>
        `;

        document.body.appendChild(notification);

        // 动画显示
        setTimeout(() => notification.classList.add('show'), 100);

        // 自动隐藏
        setTimeout(() => {
            notification.classList.remove('show');
            setTimeout(() => notification.remove(), 300);
        }, 3000);
    }

    /**
     * 获取通知图标
     */
    getNotificationIcon(type) {
        const icons = {
            'success': 'check-circle-fill',
            'error': 'exclamation-triangle-fill',
            'warning': 'exclamation-circle-fill',
            'info': 'info-circle-fill'
        };
        return icons[type] || 'info-circle-fill';
    }
    
    /**
     * 初始化WebSocket连接
     */
    initializeSocket() {
        this.socket = io();
        
        this.socket.on('connect', () => {
            this.updateConnectionStatus(true);
            this.addLog('WebSocket连接已建立', 'success');
        });
        
        this.socket.on('disconnect', () => {
            this.updateConnectionStatus(false);
            this.addLog('WebSocket连接已断开', 'warning');
        });
        
        this.socket.on('test_started', (data) => {
            this.handleTestStarted(data);
        });

        this.socket.on('test_completed', (data) => {
            this.handleTestCompleted(data);
        });

        this.socket.on('test_error', (data) => {
            this.handleTestError(data);
        });

        this.socket.on('test_progress', (data) => {
            this.updateProgress(data.progress, data.message);
        });

        this.socket.on('execution_stopped', (data) => {
            this.handleExecutionStopped(data);
        });
    }
    
    /**
     * 绑定事件处理器
     */
    bindEvents() {
        // 刷新按钮
        document.getElementById('refreshBtn').addEventListener('click', () => {
            this.loadTestCases();
        });
        
        // 全选按钮
        document.getElementById('selectAllBtn').addEventListener('click', () => {
            this.selectAllTests();
        });
        
        // 清除选择按钮
        document.getElementById('clearSelectionBtn').addEventListener('click', () => {
            this.clearSelection();
        });
        
        // 执行选中测试按钮
        document.getElementById('executeSelectedBtn').addEventListener('click', () => {
            this.executeSelectedTests();
        });
        
        // 执行所有测试按钮
        document.getElementById('executeAllBtn').addEventListener('click', () => {
            this.executeAllTests();
        });
        
        // 停止执行按钮
        document.getElementById('stopExecutionBtn').addEventListener('click', () => {
            this.stopExecution();
        });
        
        // 查看报告按钮
        document.getElementById('viewReportsBtn').addEventListener('click', () => {
            this.viewReports();
        });
        
        // 清除日志按钮
        document.getElementById('clearLogsBtn').addEventListener('click', () => {
            this.clearLogs();
        });
    }
    
    /**
     * 更新连接状态
     */
    updateConnectionStatus(connected) {
        const statusElement = document.getElementById('connectionStatus');
        if (connected) {
            statusElement.innerHTML = '<i class="bi bi-wifi"></i> 已连接';
            statusElement.className = 'badge bg-success';
        } else {
            statusElement.innerHTML = '<i class="bi bi-wifi-off"></i> 已断开';
            statusElement.className = 'badge bg-danger';
        }
    }
    
    /**
     * 加载测试用例
     */
    async loadTestCases() {
        try {
            this.showLoading(true);
            this.hideError();
            
            const response = await fetch('/api/test-cases');
            const data = await response.json();
            
            if (data.success) {
                this.testCases = data.test_cases;
                this.renderTestCases();
                this.addLog(`成功加载 ${data.total} 个测试用例`, 'info');
            } else {
                this.showError('加载测试用例失败: ' + data.error);
            }
        } catch (error) {
            this.showError('网络错误: ' + error.message);
        } finally {
            this.showLoading(false);
        }
    }
    
    /**
     * 渲染测试用例列表
     */
    renderTestCases() {
        const container = document.getElementById('testCasesList');
        
        // 按文件分组
        const fileGroups = {};
        this.testCases.forEach(testCase => {
            if (!fileGroups[testCase.file]) {
                fileGroups[testCase.file] = [];
            }
            fileGroups[testCase.file].push(testCase);
        });
        
        let html = '';
        Object.keys(fileGroups).forEach(fileName => {
            const tests = fileGroups[fileName];
            html += `
                <div class="test-file-group">
                    <div class="file-header">
                        <i class="bi bi-file-code"></i> ${fileName}
                        <span class="badge bg-secondary ms-2">${tests.length} 个测试</span>
                        <button class="btn btn-sm btn-outline-primary ms-2 execute-file-btn" 
                                data-file="${tests[0].file_path}">
                            <i class="bi bi-play"></i> 执行整个文件
                        </button>
                    </div>
            `;
            
            tests.forEach(testCase => {
                const testId = `${testCase.file_path}::${testCase.full_name}`;
                html += `
                    <div class="test-method" data-test-id="${testId}">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <input type="checkbox" class="form-check-input me-2 test-checkbox" 
                                       data-test-id="${testId}">
                                <strong>${testCase.method}</strong>
                                ${testCase.class ? `<small class="text-muted">in ${testCase.class}</small>` : ''}
                            </div>
                            <button class="btn btn-sm btn-outline-success execute-single-btn" 
                                    data-file="${testCase.file_path}" 
                                    data-test="${testCase.full_name}">
                                <i class="bi bi-play-fill"></i>
                            </button>
                        </div>
                    </div>
                `;
            });
            
            html += '</div>';
        });
        
        container.innerHTML = html;
        container.style.display = 'block';
        
        // 绑定事件
        this.bindTestCaseEvents();
    }
    
    /**
     * 绑定测试用例相关事件
     */
    bindTestCaseEvents() {
        // 复选框事件
        document.querySelectorAll('.test-checkbox').forEach(checkbox => {
            checkbox.addEventListener('change', (e) => {
                const testId = e.target.dataset.testId;
                if (e.target.checked) {
                    this.selectedTests.add(testId);
                } else {
                    this.selectedTests.delete(testId);
                }
                this.updateSelectedTestsInfo();
            });
        });
        
        // 单个测试执行按钮
        document.querySelectorAll('.execute-single-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const filePath = e.target.closest('.execute-single-btn').dataset.file;
                const testName = e.target.closest('.execute-single-btn').dataset.test;
                this.executeSingleTest(filePath, testName);
            });
        });
        
        // 文件执行按钮
        document.querySelectorAll('.execute-file-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const filePath = e.target.dataset.file;
                this.executeFile(filePath);
            });
        });
    }
    
    /**
     * 更新选中测试信息
     */
    updateSelectedTestsInfo() {
        const infoElement = document.getElementById('selectedTestsInfo');
        const executeBtn = document.getElementById('executeSelectedBtn');
        
        if (this.selectedTests.size === 0) {
            infoElement.innerHTML = '<small class="text-muted">请选择要执行的测试用例</small>';
            executeBtn.disabled = true;
        } else {
            infoElement.innerHTML = `
                <small class="text-info">
                    已选择 ${this.selectedTests.size} 个测试用例
                </small>
            `;
            executeBtn.disabled = false;
        }
    }
    
    /**
     * 执行选中的测试
     */
    async executeSelectedTests() {
        if (this.selectedTests.size === 0) {
            alert('请先选择要执行的测试用例');
            return;
        }

        if (this.isExecuting) {
            alert('已有测试正在执行中，请等待完成后再试');
            return;
        }

        try {
            this.setExecutionState(true);
            this.addLog(`开始执行 ${this.selectedTests.size} 个选中的测试`, 'info');

            const response = await fetch('/api/execute-test', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    test_list: Array.from(this.selectedTests)
                })
            });

            const data = await response.json();
            if (!data.success) {
                throw new Error(data.error);
            }

        } catch (error) {
            this.addLog(`执行失败: ${error.message}`, 'error');
            this.setExecutionState(false);
        }
    }

    /**
     * 执行所有测试
     */
    async executeAllTests() {
        if (this.isExecuting) {
            alert('已有测试正在执行中，请等待完成后再试');
            return;
        }

        const confirmed = confirm('确定要执行所有测试用例吗？这可能需要较长时间。');
        if (!confirmed) {
            return;
        }

        try {
            this.setExecutionState(true);
            this.addLog('开始执行所有测试用例', 'info');

            const response = await fetch('/api/execute-all', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                }
            });

            const data = await response.json();
            if (!data.success) {
                throw new Error(data.error);
            }

        } catch (error) {
            this.addLog(`执行失败: ${error.message}`, 'error');
            this.setExecutionState(false);
        }
    }

    /**
     * 执行单个测试
     */
    async executeSingleTest(filePath, testName) {
        if (this.isExecuting) {
            alert('已有测试正在执行中，请等待完成后再试');
            return;
        }

        try {
            this.setExecutionState(true);
            this.addLog(`开始执行测试: ${testName}`, 'info');

            const response = await fetch('/api/execute-test', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    file_path: filePath,
                    test_name: testName
                })
            });

            const data = await response.json();
            if (!data.success) {
                throw new Error(data.error);
            }

        } catch (error) {
            this.addLog(`执行失败: ${error.message}`, 'error');
            this.setExecutionState(false);
        }
    }
    
    /**
     * 执行整个文件
     */
    async executeFile(filePath) {
        if (this.isExecuting) {
            alert('已有测试正在执行中，请等待完成后再试');
            return;
        }
        
        try {
            this.setExecutionState(true);
            this.addLog(`开始执行文件: ${filePath}`, 'info');
            
            const response = await fetch('/api/execute-test', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    file_path: filePath
                })
            });
            
            const data = await response.json();
            if (!data.success) {
                throw new Error(data.error);
            }
            
        } catch (error) {
            this.addLog(`执行失败: ${error.message}`, 'error');
            this.setExecutionState(false);
        }
    }
    
    /**
     * 设置执行状态
     */
    setExecutionState(executing) {
        this.isExecuting = executing;
        
        const executeSelectedBtn = document.getElementById('executeSelectedBtn');
        const executeAllBtn = document.getElementById('executeAllBtn');
        const stopBtn = document.getElementById('stopExecutionBtn');
        const statusPanel = document.getElementById('executionStatus');
        
        if (executing) {
            executeSelectedBtn.disabled = true;
            executeAllBtn.disabled = true;
            stopBtn.disabled = false;
            statusPanel.style.display = 'block';
            this.updateProgress(0, '准备执行...');
        } else {
            executeSelectedBtn.disabled = this.selectedTests.size === 0;
            executeAllBtn.disabled = false;
            stopBtn.disabled = true;
            statusPanel.style.display = 'none';
        }
    }
    
    /**
     * 更新进度
     */
    updateProgress(progress, message) {
        const progressBar = document.getElementById('progressBar');
        const progressText = document.getElementById('progressText');
        
        progressBar.style.width = progress + '%';
        progressBar.setAttribute('aria-valuenow', progress);
        progressText.textContent = message || `${progress}%`;
    }
    
    /**
     * 停止执行
     */
    async stopExecution() {
        if (!this.isExecuting) {
            return;
        }

        const confirmed = confirm('确定要停止当前测试执行吗？');
        if (!confirmed) {
            return;
        }

        try {
            const response = await fetch('/api/stop-execution', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                }
            });

            const data = await response.json();
            if (data.success) {
                this.addLog('测试执行已停止', 'warning');
            }

        } catch (error) {
            this.addLog(`停止执行失败: ${error.message}`, 'error');
        }
    }

    /**
     * 处理测试开始
     */
    handleTestStarted(data) {
        this.addLog(data.message || '测试开始执行', 'info');
        document.getElementById('currentTestInfo').textContent = data.test || '执行中...';
    }

    /**
     * 处理测试完成
     */
    handleTestCompleted(data) {
        this.setExecutionState(false);
        this.addLog('测试执行完成', 'success');

        // 显示详细输出
        if (data.output) {
            this.addLog('=== 测试输出 ===', 'info');
            this.addLog(data.output, 'info');
        }

        if (data.error) {
            this.addLog('=== 错误信息 ===', 'error');
            this.addLog(data.error, 'error');
        }

        // 显示结果摘要
        this.showResultsSummary(data.results);
    }

    /**
     * 处理测试错误
     */
    handleTestError(data) {
        this.setExecutionState(false);
        this.addLog(`测试执行出错: ${data.error}`, 'error');
    }

    /**
     * 处理执行停止
     */
    handleExecutionStopped(data) {
        this.setExecutionState(false);
        this.addLog(data.message || '测试执行已停止', 'warning');
    }
    
    /**
     * 显示结果摘要
     */
    showResultsSummary(results) {
        const summaryElement = document.getElementById('resultsSummary');
        const contentElement = document.getElementById('summaryContent');
        
        let passed = 0, failed = 0, errors = 0;
        results.forEach(result => {
            if (result.status === 'PASSED') passed++;
            else if (result.status === 'FAILED') failed++;
            else errors++;
        });
        
        contentElement.innerHTML = `
            <div class="row text-center">
                <div class="col-4">
                    <div class="text-success">
                        <i class="bi bi-check-circle"></i><br>
                        <strong>${passed}</strong><br>
                        <small>通过</small>
                    </div>
                </div>
                <div class="col-4">
                    <div class="text-danger">
                        <i class="bi bi-x-circle"></i><br>
                        <strong>${failed}</strong><br>
                        <small>失败</small>
                    </div>
                </div>
                <div class="col-4">
                    <div class="text-warning">
                        <i class="bi bi-exclamation-triangle"></i><br>
                        <strong>${errors}</strong><br>
                        <small>错误</small>
                    </div>
                </div>
            </div>
        `;
        
        summaryElement.style.display = 'block';
    }
    
    /**
     * 添加日志
     */
    addLog(message, type = 'info') {
        const logsElement = document.getElementById('executionLogs');
        const timestamp = new Date().toLocaleTimeString();
        const typeClass = {
            'info': 'text-info',
            'success': 'text-success',
            'warning': 'text-warning',
            'error': 'text-danger'
        }[type] || 'text-info';
        
        const logEntry = `[${timestamp}] ${message}\n`;
        logsElement.innerHTML += `<span class="${typeClass}">${logEntry}</span>`;
        logsElement.scrollTop = logsElement.scrollHeight;
    }
    
    /**
     * 清除日志
     */
    clearLogs() {
        document.getElementById('executionLogs').innerHTML = '';
    }
    
    /**
     * 显示/隐藏加载状态
     */
    showLoading(show) {
        const spinner = document.getElementById('loadingSpinner');
        const list = document.getElementById('testCasesList');
        
        if (show) {
            spinner.style.display = 'block';
            list.style.display = 'none';
        } else {
            spinner.style.display = 'none';
            list.style.display = 'block';
        }
    }
    
    /**
     * 显示错误信息
     */
    showError(message) {
        const errorElement = document.getElementById('errorMessage');
        errorElement.textContent = message;
        errorElement.style.display = 'block';
    }
    
    /**
     * 隐藏错误信息
     */
    hideError() {
        document.getElementById('errorMessage').style.display = 'none';
    }
    
    /**
     * 全选测试
     */
    selectAllTests() {
        document.querySelectorAll('.test-checkbox').forEach(checkbox => {
            checkbox.checked = true;
            this.selectedTests.add(checkbox.dataset.testId);
        });
        this.updateSelectedTestsInfo();
    }
    
    /**
     * 清除选择
     */
    clearSelection() {
        document.querySelectorAll('.test-checkbox').forEach(checkbox => {
            checkbox.checked = false;
        });
        this.selectedTests.clear();
        this.updateSelectedTestsInfo();
    }
    
    /**
     * 查看报告
     */
    async viewReports() {
        try {
            this.addLog('正在生成测试报告...', 'info');

            // 先生成报告
            const generateResponse = await fetch('/api/generate-report');
            const generateData = await generateResponse.json();

            if (!generateData.success) {
                throw new Error(generateData.error);
            }

            this.addLog('报告生成成功，正在打开...', 'success');

            // 打开报告页面
            window.open('/api/view-report', '_blank');

        } catch (error) {
            this.addLog(`报告查看失败: ${error.message}`, 'error');

            // 如果是没有测试结果，提示用户先执行测试
            if (error.message.includes('没有找到测试结果文件')) {
                alert('请先执行测试用例，然后再查看报告');
            }
        }
    }
}

// 页面加载完成后初始化应用
document.addEventListener('DOMContentLoaded', () => {
    new TestExecutionController();
});
