
from selenium.webdriver.common.by import By



# 供应商
class KnowledgeManagementPageLocs:
    # 供应商菜单
    knowledge_management_loc = (By.XPATH, "//a[contains(text(), '供应商')]")
    # 创建供应商按钮
    create_knowledge_management_loc  = (By.XPATH,"//span[contains(text(), '创建供应商')]")
    # 供应商编号
    knowledge_management_code_loc = (By.XPATH,'(//*[@ id="vendorCode"])[2]')
    # 供应商名称
    knowledge_management_name_loc = (By.XPATH, '(//*[@ id="shortName"])[2]')
    # 供应商全称
    knowledge_management_full_name_loc = (By.XPATH, '(//*[@ id="vendorName"])[2]')
    # 付款期限
    pay_days_loc = (By.XPATH, '(//*[@id="payableDays"])[3]')
    # 联系人
    contacts_loc = (By.XPATH, '(//*[@ id="contactPerson"])[2]')
    # 联系电话
    contact_phone_loc = (By.XPATH, '(//*[@ id="contactPhone"])[2]')
    # 联系地址
    contact_address_loc = (By.XPATH, '(//*[@ id="contactAddress"])[2]')
    # 确定
    sure_loc = (By.XPATH, "//span[contains(text(), '确 定')]")
    # 创建成功提示
    sure_success_loc = (By.XPATH, "//span[contains(text(), '创建成功')]")









