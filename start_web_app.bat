@echo off
chcp 65001 >nul
setlocal enabledelayedexpansion

REM BlackLake测试框架 - Windows Web应用启动脚本
REM 版本: 1.0.0

echo.
echo 🎯 BlackLake测试框架 - Web应用启动器
echo ==================================================

REM 检查Python是否安装
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 未找到Python！请先安装Python 3.8+
    pause
    exit /b 1
)

REM 检查虚拟环境
if exist ".venv\Scripts\activate.bat" (
    echo ✅ 发现虚拟环境，正在激活...
    call .venv\Scripts\activate.bat
) else if exist "venv\Scripts\activate.bat" (
    echo ✅ 发现虚拟环境，正在激活...
    call venv\Scripts\activate.bat
) else (
    echo ⚠️ 未找到虚拟环境，使用系统Python
)

REM 设置环境变量
set PYTHONPATH=%CD%
set BLACKLAKE_PLATFORM=windows

REM 检查应用文件
if not exist "web_app.py" (
    if not exist "simple_web_app.py" (
        echo ❌ 未找到Web应用文件！
        pause
        exit /b 1
    )
    set APP_FILE=simple_web_app.py
    echo ℹ️ 使用简化版Web应用
) else (
    set APP_FILE=web_app.py
    echo ℹ️ 使用完整版Web应用
)

REM 启动Web应用
echo.
echo 🚀 启动BlackLake测试框架Web应用
echo 应用文件: !APP_FILE!
echo 访问地址: http://localhost:5000
echo 平台: Windows
echo.
echo 按 Ctrl+C 停止应用
echo.

REM 启动应用
python start_web_app.py --host 0.0.0.0 --port 5000

REM 如果启动失败，尝试直接运行
if errorlevel 1 (
    echo.
    echo ⚠️ 启动器失败，尝试直接运行应用...
    python !APP_FILE!
)

echo.
echo 👋 Web应用已停止
pause
