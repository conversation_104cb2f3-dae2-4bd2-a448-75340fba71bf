[log]
account = 009516
name = admin
password = admin
level = INFO
file_ok = True
file_name = blacklake_mac.log

[browser]
default_browser = chrome
headless = False
window_size = 1920,1080
implicit_wait = 10
page_load_timeout = 30
enable_logging = True

[platform]
os_type = mac
driver_auto_download = True
screenshot_format = png
screenshot_quality = 90

[chrome_options]
# Mac专用Chrome选项
disable_web_security = True
allow_running_insecure_content = True
disable_features = VizDisplayCompositor
no_sandbox = True
disable_dev_shm_usage = True
disable_gpu = True
disable_extensions = True

[paths]
# Mac专用路径配置
chrome_binary = /Applications/Google Chrome.app/Contents/MacOS/Google Chrome
downloads_path = ~/Downloads
temp_path = /tmp/blacklake_tests

[security]
# Mac安全设置
allow_untrusted_certs = True
ignore_ssl_errors = True
disable_popup_blocking = True
