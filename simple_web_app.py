#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
简化版Web应用 - 不依赖SocketIO，避免SSL问题
"""

import os
import sys
import json
import subprocess
import threading
import time
import glob
from datetime import datetime
from pathlib import Path
from flask import Flask, render_template, request, jsonify, send_file

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

from Common.handle_path import cases_dir, reports_dir
from session_manager import session_manager
from pytest_parser import pytest_parser, reset_batch_statistics, add_file_result, get_batch_statistics, get_batch_summary, set_total_files

app = Flask(__name__)
app.config['SECRET_KEY'] = 'test_execution_control_secret_key'
# 禁用模板缓存以确保修改立即生效
app.config['TEMPLATES_AUTO_RELOAD'] = True
app.jinja_env.auto_reload = True
app.config['SEND_FILE_MAX_AGE_DEFAULT'] = 0

# 全局变量存储测试执行状态
test_execution_status = {
    'running': False,
    'current_test': None,
    'progress': 0,
    'total_tests': 0,
    'results': [],
    'start_time': None,
    'end_time': None,
    'logs': [],
    'stop_requested': False  # 添加停止请求标志
}

# 全局进程管理
current_processes = []  # 存储当前运行的进程
execution_lock = threading.Lock()  # 执行锁
current_execution_thread = None  # 当前执行线程
execution_request_id = None  # 执行请求ID，用于防重复

def add_process(process):
    """添加进程到管理列表"""
    with execution_lock:
        current_processes.append(process)
        print(f"📝 添加进程到管理列表: PID {process.pid}")

def remove_process(process):
    """从管理列表移除进程"""
    with execution_lock:
        if process in current_processes:
            current_processes.remove(process)
            print(f"🗑️ 从管理列表移除进程: PID {process.pid}")

def kill_all_processes_no_lock():
    """终止所有当前进程（不使用锁，用于已在锁内的情况）"""
    killed_count = 0

    # 首先尝试终止测试执行器的当前进程
    if hasattr(test_executor, 'current_process') and test_executor.current_process:
        try:
            if test_executor.current_process.poll() is None:
                print(f"🔪 终止测试执行器进程: PID {test_executor.current_process.pid}")
                test_executor.current_process.terminate()
                try:
                    test_executor.current_process.wait(timeout=3)
                except subprocess.TimeoutExpired:
                    print(f"⚡ 强制杀死测试执行器进程: PID {test_executor.current_process.pid}")
                    test_executor.current_process.kill()
                    test_executor.current_process.wait()
                killed_count += 1
            test_executor.current_process = None
        except Exception as e:
            print(f"❌ 终止测试执行器进程失败: {e}")

    # 终止所有管理的进程
    global current_processes
    for process in current_processes[:]:  # 使用副本避免修改列表时的问题
        try:
            if process.poll() is None:
                print(f"🔪 终止管理进程: PID {process.pid}")
                process.terminate()
                try:
                    process.wait(timeout=3)
                except subprocess.TimeoutExpired:
                    print(f"⚡ 强制杀死管理进程: PID {process.pid}")
                    process.kill()
                    process.wait()
                killed_count += 1
            current_processes.remove(process)
        except Exception as e:
            print(f"❌ 终止管理进程失败: {e}")
            try:
                current_processes.remove(process)
            except ValueError:
                pass

    return killed_count

def kill_all_processes():
    """终止所有当前进程（使用锁）"""
    with execution_lock:
        return kill_all_processes_no_lock()



def is_stop_requested():
    """检查是否请求停止"""
    return test_execution_status.get('stop_requested', False)

def reset_stop_flag():
    """重置停止标志"""
    test_execution_status['stop_requested'] = False

class TestCaseDiscovery:
    """测试用例发现和管理类"""
    
    def __init__(self, test_dir=None):
        self.test_dir = test_dir or cases_dir
        
    def discover_test_files(self):
        """发现所有测试文件"""
        test_files = []
        patterns = [
            os.path.join(self.test_dir, "test_*.py"),
            os.path.join(self.test_dir, "*test*.py"),
            os.path.join(self.test_dir, "atest_*.py")
        ]
        
        for pattern in patterns:
            for file_path in glob.glob(pattern):
                if os.path.basename(file_path) not in ['conftest.py', '__init__.py']:
                    test_files.append(file_path)
        
        return list(set(test_files))
    
    def parse_test_cases(self, file_path):
        """解析单个文件中的测试用例"""
        test_cases = []
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
                
            lines = content.split('\n')
            current_class = None
            
            for line_num, line in enumerate(lines, 1):
                stripped_line = line.strip()
                
                if stripped_line.startswith('class Test') and ':' in stripped_line:
                    current_class = stripped_line.split('class ')[1].split('(')[0].split(':')[0].strip()
                
                elif stripped_line.startswith('def test_') and '(' in stripped_line:
                    method_name = stripped_line.split('def ')[1].split('(')[0].strip()
                    test_cases.append({
                        'file': os.path.basename(file_path),
                        'file_path': file_path,
                        'class': current_class,
                        'method': method_name,
                        'line': line_num,
                        'full_name': f"{current_class}::{method_name}" if current_class else method_name,
                        'test_id': f"{file_path}::{current_class}::{method_name}" if current_class else f"{file_path}::{method_name}"
                    })
                    
        except Exception as e:
            print(f"解析文件 {file_path} 时出错: {e}")
            
        return test_cases
    
    def get_all_test_cases(self):
        """获取所有测试用例"""
        all_cases = []
        test_files = self.discover_test_files()
        
        for file_path in test_files:
            cases = self.parse_test_cases(file_path)
            all_cases.extend(cases)
            
        return all_cases

class TestExecutor:
    """测试执行器"""

    def __init__(self):
        self.discovery = TestCaseDiscovery()
        self.current_process = None

    def is_process_completed(self, output_line):
        """检测是否包含进程结束标识"""
        if not output_line:
            return False, None

        # 检测"进程已结束，退出代码为 X"的标识
        import re
        patterns = [
            r'进程已结束，退出代码为\s*(\d+)',
            r'Process finished with exit code\s*(\d+)',
            r'进程结束.*退出代码.*?(\d+)',
            r'exit code\s*[:\s]*(\d+)',
        ]

        for pattern in patterns:
            match = re.search(pattern, output_line, re.IGNORECASE)
            if match:
                exit_code = int(match.group(1))
                print(f"🎯 发现进程结束标识: {output_line.strip()}")
                print(f"📊 提取的退出代码: {exit_code}")
                return True, exit_code

        return False, None

    def is_pytest_execution_complete(self, line):
        """检测pytest执行是否完成的标识"""
        if not line:
            return False

        # pytest执行完成的标识模式
        completion_patterns = [
            "passed in",           # "3 passed in 2.34s"
            "failed in",           # "1 failed, 2 passed in 3.45s"
            "error in",            # "1 error in 1.23s"
            "skipped in",          # "3 skipped in 0.12s"
            "warnings summary",    # pytest warnings summary (通常在最后)
            "short test summary",  # pytest short test summary
        ]

        line_lower = line.lower()

        # 检查是否包含完成标识
        for pattern in completion_patterns:
            if pattern in line_lower and ("=" * 5) in line:  # 确保是pytest的分隔线格式
                print(f"🎯 发现pytest执行结束标识: {line}")
                return True

        # 检查是否是pytest的结果总结行
        if line.startswith("=") and line.endswith("=") and len(line) > 20:
            if any(keyword in line_lower for keyword in ["passed", "failed", "error", "skipped"]):
                if any(time_keyword in line_lower for time_keyword in ["in", "s"]):
                    print(f"🎯 发现pytest时间总结标识: {line}")
                    return True

        return False
        
    def execute_test(self, file_path, test_name=None, request_id=None):
        """执行测试用例"""
        global test_execution_status, execution_request_id

        print(f"🎯 TestExecutor.execute_test 开始")
        print(f"   文件路径: {file_path}")
        print(f"   测试名称: {test_name}")
        print(f"🆔 请求ID: {request_id}, 当前ID: {execution_request_id}")

        # 验证请求ID，防止重复执行
        if request_id and request_id != execution_request_id:
            print(f"⚠️ 请求ID不匹配，忽略执行: {request_id} != {execution_request_id}")
            return

        # 开始新会话
        session_id = session_manager.start_session(
            execution_type="single" if test_name else "file",
            files=[file_path]
        )

        test_execution_status['running'] = True
        test_execution_status['start_time'] = datetime.now()
        test_execution_status['current_test'] = test_name or os.path.basename(file_path)
        test_execution_status['progress'] = 0
        test_execution_status['results'] = []
        test_execution_status['logs'] = []
        test_execution_status['session_id'] = session_id
        reset_stop_flag()  # 重置停止标志

        # 添加初始日志
        test_execution_status['logs'].append(f"开始执行: {os.path.basename(file_path)}")
        test_execution_status['logs'].append(f"会话ID: {session_id}")
        print(f"✅ 初始状态设置完成，会话ID: {session_id}")

        try:
            # 检查pytest是否可用
            try:
                import pytest
                test_execution_status['logs'].append(f"使用pytest版本: {pytest.__version__}")
            except ImportError:
                raise Exception("pytest未安装，请运行: pip install pytest")

            # 构建pytest命令 - 简化版本避免兼容性问题
            if test_name and '::' in test_name:
                cmd = [
                    sys.executable, '-m', 'pytest',
                    f"{file_path}::{test_name}",
                    '-v', '--tb=short'
                ]
            else:
                cmd = [
                    sys.executable, '-m', 'pytest',
                    file_path,
                    '-v', '--tb=short'
                ]

            # 只在allure可用时添加allure参数
            try:
                import allure_pytest
                cmd.extend([f'--alluredir={reports_dir}/allure-results'])
                test_execution_status['logs'].append("启用Allure报告生成")
            except ImportError:
                test_execution_status['logs'].append("Allure不可用，跳过报告生成")

            test_execution_status['progress'] = 10
            test_execution_status['logs'].append(f"执行命令: {' '.join(cmd)}")

            # 检查是否请求停止
            if is_stop_requested():
                test_execution_status['logs'].append("🛑 检测到停止请求，取消执行")
                return

            # 执行测试
            self.current_process = subprocess.Popen(
                cmd,
                cwd=project_root,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                encoding='utf-8'
            )

            # 添加进程到管理列表
            add_process(self.current_process)

            try:
                # 检查停止请求的同时等待进程完成
                while self.current_process.poll() is None:
                    if is_stop_requested():
                        test_execution_status['logs'].append("🛑 收到停止请求，正在终止进程...")
                        self.current_process.terminate()
                        try:
                            self.current_process.wait(timeout=5)
                        except subprocess.TimeoutExpired:
                            self.current_process.kill()
                            self.current_process.wait()
                        test_execution_status['logs'].append("✅ 进程已终止")
                        return
                    time.sleep(0.1)  # 短暂等待避免CPU占用过高

                stdout, stderr = self.current_process.communicate()
            finally:
                # 从管理列表移除进程
                remove_process(self.current_process)

            test_execution_status['progress'] = 100
            test_execution_status['end_time'] = datetime.now()

            # 检查是否是pytest兼容性错误
            if ("cannot import name 'FixtureDef'" in stderr or
                "PluggyTeardownRaisedWarning" in stderr or
                "pytest_asyncio" in stderr):

                test_execution_status['logs'].append("🔥 检测到pytest兼容性问题!")
                test_execution_status['logs'].append("💡 这是pytest-asyncio插件与pytest版本不兼容")
                test_execution_status['logs'].append("🔧 修复方法:")
                test_execution_status['logs'].append("   1. 运行: python fix_pytest_simple.py")
                test_execution_status['logs'].append("   2. 或手动执行:")
                test_execution_status['logs'].append("      pip uninstall pytest pytest-asyncio -y")
                test_execution_status['logs'].append("      pip install pytest==7.4.2 pytest-asyncio==0.21.1")

                test_execution_status['results'] = [{
                    'test': test_name or os.path.basename(file_path),
                    'status': 'ERROR',
                    'error': 'pytest兼容性问题 - 请运行 fix_pytest_simple.py 修复',
                    'output': stdout,
                    'stderr': stderr[:500] + "..." if len(stderr) > 500 else stderr
                }]
            else:
                # 使用pytest解析器解析详细结果
                test_results, statistics = pytest_parser.parse_output(stdout, stderr)
                duration = (test_execution_status['end_time'] - test_execution_status['start_time']).total_seconds()

                # 转换为前端格式
                frontend_results = []
                for result in test_results:
                    frontend_results.append({
                        'test': result.test_name,
                        'status': result.status,
                        'output': stdout[:500] if stdout else "",
                        'error': result.error_message or stderr,
                        'duration': result.duration,
                        'file_path': result.file_path,
                        'class_name': result.class_name,
                        'method_name': result.method_name
                    })

                # 如果没有解析到具体结果，使用整体结果
                if not frontend_results:
                    success = self.current_process.returncode == 0
                    frontend_results = [{
                        'test': test_name or os.path.basename(file_path),
                        'status': 'PASSED' if success else 'FAILED',
                        'output': stdout,
                        'error': stderr,
                        'duration': duration
                    }]

                test_execution_status['results'] = frontend_results
                test_execution_status['statistics'] = statistics

                # 添加每个测试结果到会话管理器
                for result in test_results:
                    session_manager.add_test_result(
                        test_name=result.test_name,
                        status=result.status,
                        duration=result.duration,
                        error_message=result.error_message if result.error_message else None,
                        output=stdout[:200] if stdout else None  # 限制输出长度
                    )

                # 如果没有具体结果，添加整体结果
                if not test_results:
                    success = self.current_process.returncode == 0
                    session_manager.add_test_result(
                        test_name=test_name or os.path.basename(file_path),
                        status='PASSED' if success else 'FAILED',
                        duration=duration,
                        error_message=stderr if stderr else None,
                        output=stdout[:200] if stdout else None
                    )

            # 添加日志
            test_execution_status['logs'].append(f"测试执行完成: {test_name or file_path}")
            if stdout:
                test_execution_status['logs'].append("=== 输出 ===")
                test_execution_status['logs'].append(stdout[:1000] + "..." if len(stdout) > 1000 else stdout)
            if stderr:
                test_execution_status['logs'].append("=== 错误 ===")
                test_execution_status['logs'].append(stderr[:1000] + "..." if len(stderr) > 1000 else stderr)

        except Exception as e:
            test_execution_status['results'] = [{
                'test': test_name or file_path,
                'status': 'ERROR',
                'error': str(e)
            }]
            test_execution_status['logs'].append(f"执行出错: {str(e)}")

        finally:
            # 结束会话
            session_manager.end_session()

            test_execution_status['running'] = False
            test_execution_status['current_test'] = None
            self.current_process = None

    def execute_multiple_files(self, file_list, request_id=None):
        """执行多个测试文件"""
        global test_execution_status, execution_request_id

        print(f"🎬 开始执行批量文件方法，线程: {threading.current_thread().name}")
        print(f"🆔 请求ID: {request_id}, 当前ID: {execution_request_id}")
        print(f"🔍 请求ID类型: {type(request_id)}, 当前ID类型: {type(execution_request_id)}")

        # 验证请求ID，防止重复执行
        if request_id and request_id != execution_request_id:
            print(f"⚠️ 请求ID不匹配，忽略执行: {request_id} != {execution_request_id}")
            print(f"🔍 详细比较: '{request_id}' != '{execution_request_id}'")
            # 强制重置状态，因为执行被忽略了
            test_execution_status['running'] = False
            return

        print("✅ 请求ID验证通过，继续执行")

        # 不再重复设置running状态，因为API端点已经设置了
        # test_execution_status['running'] = True  # 注释掉，避免重复设置
        test_execution_status['start_time'] = datetime.now()
        test_execution_status['progress'] = 0
        test_execution_status['total_tests'] = len(file_list)
        test_execution_status['results'] = []
        test_execution_status['logs'] = []
        reset_stop_flag()  # 重置停止标志

        # 初始化批量统计
        reset_batch_statistics()
        set_total_files(len(file_list))
        print(f"📊 初始化批量统计: 总文件数 {len(file_list)}")

        try:
            print(f"🚀 开始批量执行 {len(file_list)} 个测试文件")
            test_execution_status['logs'].append(f"开始批量执行 {len(file_list)} 个测试文件")

            for i, file_path in enumerate(file_list):
                print(f"📁 处理第 {i+1}/{len(file_list)} 个文件: {file_path}")
                # 检查是否请求停止
                if is_stop_requested():
                    test_execution_status['logs'].append(f"🛑 收到停止请求，已执行 {i}/{len(file_list)} 个文件")
                    break
                file_name = file_path.split('/')[-1] if '/' in file_path else file_path.split('\\')[-1]
                test_execution_status['current_test'] = file_name

                # 更新进度：开始执行时的进度
                start_progress = int((i / len(file_list)) * 100)
                test_execution_status['progress'] = start_progress
                print(f"📊 进度更新: {start_progress}% (开始执行第 {i+1}/{len(file_list)} 个文件)")
                test_execution_status['logs'].append(f"📊 进度更新: {start_progress}% (开始执行第 {i+1}/{len(file_list)} 个文件)")

                # 执行单个文件
                cmd = [
                    sys.executable, '-m', 'pytest',
                    file_path,
                    '-v', '--tb=short', '--no-header',
                    f'--alluredir={reports_dir}/allure-results'
                ]

                test_execution_status['logs'].append(f"正在执行: {file_name}")

                # 再次检查停止请求
                if is_stop_requested():
                    test_execution_status['logs'].append(f"🛑 在执行 {file_name} 前收到停止请求")
                    break

                self.current_process = subprocess.Popen(
                    cmd,
                    cwd=project_root,
                    stdout=subprocess.PIPE,
                    stderr=subprocess.PIPE,
                    text=True,
                    encoding='utf-8'
                )

                # 添加进程到管理列表
                add_process(self.current_process)

                try:
                    # 监控控制台输出，寻找执行结束标识
                    print(f"🕐 监控pytest控制台输出，等待执行结束标识... PID={self.current_process.pid}")
                    start_time = time.time()

                    # 收集输出
                    stdout_lines = []
                    stderr_lines = []
                    execution_completed = False
                    returncode = None

                    # 添加超时保护机制，避免进程卡住
                    max_execution_time = 120  # 2分钟超时，更快检测问题

                    while True:
                        # 检查停止请求
                        if is_stop_requested():
                            test_execution_status['logs'].append(f"🛑 在执行 {file_name} 时收到停止请求，正在终止...")
                            self.current_process.terminate()
                            try:
                                self.current_process.wait(timeout=5)
                            except subprocess.TimeoutExpired:
                                self.current_process.kill()
                                self.current_process.wait()
                            test_execution_status['logs'].append(f"✅ {file_name} 进程已终止")
                            return

                        # 更激进的进程检测策略
                        try:
                            # 方法1: 使用poll()检测
                            current_returncode = self.current_process.poll()
                            if current_returncode is not None:
                                # 进程已结束
                                returncode = current_returncode
                                elapsed_time = time.time() - start_time
                                print(f"🎉 poll()检测到进程结束！{file_name} 用时: {elapsed_time:.1f}秒")
                                print(f"📊 进程返回码: {returncode}")
                                print(f"✅ 进程已结束，退出代码为 {returncode}")
                                execution_completed = True
                                break

                            # 方法2: 使用wait()非阻塞检测
                            try:
                                wait_returncode = self.current_process.wait(timeout=0.01)
                                if wait_returncode is not None:
                                    # 进程已结束
                                    returncode = wait_returncode
                                    elapsed_time = time.time() - start_time
                                    print(f"🎉 wait()检测到进程结束！{file_name} 用时: {elapsed_time:.1f}秒")
                                    print(f"📊 进程返回码: {returncode}")
                                    print(f"✅ 进程已结束，退出代码为 {returncode}")
                                    execution_completed = True
                                    break
                            except subprocess.TimeoutExpired:
                                # wait()超时，进程仍在运行
                                pass

                            # 方法3: 尝试非阻塞communicate()检测
                            if elapsed_time > 30:  # 30秒后开始尝试
                                try:
                                    # 尝试非阻塞获取输出
                                    stdout_data, stderr_data = self.current_process.communicate(timeout=0.01)
                                    # 如果能获取到输出，说明进程可能已经结束
                                    final_returncode = self.current_process.returncode
                                    if final_returncode is not None:
                                        returncode = final_returncode
                                        elapsed_time = time.time() - start_time
                                        print(f"🎉 communicate()检测到进程结束！{file_name} 用时: {elapsed_time:.1f}秒")
                                        print(f"📊 进程返回码: {returncode}")
                                        print(f"✅ 进程已结束，退出代码为 {returncode}")
                                        execution_completed = True
                                        # 保存输出数据
                                        stdout, stderr = stdout_data, stderr_data
                                        break
                                except subprocess.TimeoutExpired:
                                    # communicate()超时，进程仍在运行
                                    pass
                                except Exception as comm_e:
                                    # communicate()异常，可能进程已结束
                                    print(f"🔍 communicate()异常，检查进程状态: {comm_e}")
                                    final_returncode = self.current_process.poll()
                                    if final_returncode is not None:
                                        returncode = final_returncode
                                        elapsed_time = time.time() - start_time
                                        print(f"🎉 异常后检测到进程结束！{file_name} 用时: {elapsed_time:.1f}秒")
                                        print(f"📊 进程返回码: {returncode}")
                                        print(f"✅ 进程已结束，退出代码为 {returncode}")
                                        execution_completed = True
                                        break

                        except Exception as e:
                            print(f"⚠️ 进程检测异常: {e}")
                            # 继续检测

                        # 检查超时
                        elapsed_time = time.time() - start_time
                        if elapsed_time > max_execution_time:
                            print(f"⏰ {file_name} 执行超时 ({max_execution_time/60:.1f}分钟)，正在终止...")
                            test_execution_status['logs'].append(f"⏰ {file_name} 执行超时 ({max_execution_time/60:.1f}分钟)，正在终止...")

                            # 强制终止进程
                            self.current_process.terminate()
                            try:
                                self.current_process.wait(timeout=5)
                            except subprocess.TimeoutExpired:
                                self.current_process.kill()
                                self.current_process.wait()

                            print(f"❌ {file_name} 因超时被终止")
                            test_execution_status['logs'].append(f"❌ {file_name} 因超时被终止")

                            # 设置超时返回码
                            returncode = -1
                            execution_completed = True
                            break

                        # 更短的等待时间，提高检测频率
                        time.sleep(0.1)

                        # 每10秒打印一次进度，包含进程状态
                        if int(elapsed_time) % 10 == 0 and int(elapsed_time) > 0:
                            try:
                                # 检查进程是否还存在
                                poll_result = self.current_process.poll()
                                print(f"🕐 {file_name} 执行中... 已用时: {elapsed_time:.1f}秒, poll()={poll_result}")

                                # 如果poll()返回了值但我们没有检测到，这是一个问题
                                if poll_result is not None:
                                    print(f"🚨 警告：poll()返回{poll_result}但未被检测到！")

                            except Exception as e:
                                print(f"🕐 {file_name} 执行中... 已用时: {elapsed_time:.1f}秒 (状态检查异常: {e})")

                    # 获取完整的进程输出
                    if returncode != -1:  # 非超时情况
                        stdout, stderr = self.current_process.communicate()
                    else:  # 超时情况
                        try:
                            stdout, stderr = self.current_process.communicate(timeout=1)
                        except subprocess.TimeoutExpired:
                            stdout, stderr = "", "进程因超时被终止"

                    # 输出标准的进程结束标识
                    print(f"进程已结束，退出代码为 {returncode}")
                    print(f"✅ {file_name} 进程输出获取完成，返回码: {returncode}")

                    # 添加到日志
                    test_execution_status['logs'].append(f"进程已结束，退出代码为 {returncode}")
                    test_execution_status['logs'].append(f"✅ {file_name} 执行完成")
                finally:
                    # 从管理列表移除进程
                    remove_process(self.current_process)

                # 解析pytest输出获取详细统计
                test_results, statistics = pytest_parser.parse_output(stdout, stderr)

                # 添加到批量统计
                add_file_result(file_path, test_results, statistics)

                # 简化结果记录：基于pytest返回码和测试结果
                returncode = self.current_process.returncode

                # pytest返回码含义：
                # 0 = 所有测试通过
                # 1 = 有测试失败
                # 2 = 测试执行被用户中断
                # 3 = 内部错误
                # 4 = pytest使用错误
                # 5 = 没有找到测试
                # 只要返回码在0-5范围内，就认为进程正常完成
                process_completed = returncode is not None and 0 <= returncode <= 5

                if process_completed:
                    # 进程正常完成，根据测试结果判断成功失败
                    test_passed = statistics.get('passed', 0)
                    test_failed = statistics.get('failed', 0) + statistics.get('error', 0)
                    success = test_failed == 0 and test_passed > 0
                    result_status = 'PASSED' if success else 'FAILED'
                else:
                    # 进程异常退出
                    success = False
                    result_status = 'ERROR'

                test_execution_status['results'].append({
                    'test': file_name,
                    'status': result_status,
                    'output': stdout,
                    'error': stderr,
                    'return_code': self.current_process.returncode,
                    'test_count': len(test_results),
                    'passed': statistics.get('passed', 0),
                    'failed': statistics.get('failed', 0),
                    'skipped': statistics.get('skipped', 0),
                    'error': statistics.get('error', 0)
                })

                # 详细的结果日志
                if not process_completed:
                    # 进程异常退出
                    test_execution_status['logs'].append(f"💥 {file_name} 进程异常退出 (返回码: {self.current_process.returncode})")
                    print(f"💥 {file_name} 进程异常退出 (返回码: {self.current_process.returncode})")
                    if stderr:
                        test_execution_status['logs'].append(f"   错误信息: {stderr[:200]}...")
                        print(f"   错误信息: {stderr[:200]}...")
                elif statistics.get('total', 0) > 0:
                    # 有测试用例的情况
                    passed = statistics.get('passed', 0)
                    failed = statistics.get('failed', 0)
                    skipped = statistics.get('skipped', 0)
                    errors = statistics.get('error', 0)
                    total = statistics.get('total', 0)

                    if success:
                        test_execution_status['logs'].append(f"✅ {file_name} 执行完成 - 全部{passed}个测试通过")
                        print(f"✅ {file_name} 执行完成 - 总计{total}个测试: {passed}通过, {failed}失败, {skipped}跳过, {errors}错误")
                    else:
                        failed_total = failed + errors
                        test_execution_status['logs'].append(f"⚠️ {file_name} 执行完成 - {failed_total}个测试失败")
                        print(f"⚠️ {file_name} 执行完成 - 总计{total}个测试: {passed}通过, {failed_total}失败, {skipped}跳过")
                else:
                    # 没有测试用例或无法解析
                    test_execution_status['logs'].append(f"✅ {file_name} 执行完成 (返回码: {self.current_process.returncode})")
                    print(f"✅ {file_name} 执行完成 (返回码: {self.current_process.returncode})")

                # 更新进度：文件执行完成后的进度
                completed_progress = int(((i + 1) / len(file_list)) * 100)
                test_execution_status['progress'] = completed_progress
                print(f"📊 进度更新: {completed_progress}% (完成第 {i+1}/{len(file_list)} 个文件)")
                test_execution_status['logs'].append(f"📊 进度更新: {completed_progress}% (完成第 {i+1}/{len(file_list)} 个文件)")

                # 检查是否还有下一个文件
                if i + 1 < len(file_list):
                    next_file = file_list[i + 1].split('/')[-1] if '/' in file_list[i + 1] else file_list[i + 1].split('\\')[-1]
                    print(f"🔄 继续执行下一个文件: {next_file}")
                    test_execution_status['logs'].append(f"🔄 继续执行下一个文件...")

                    # 立即更新下一个文件的开始进度
                    next_start_progress = int(((i + 1) / len(file_list)) * 100)
                    test_execution_status['progress'] = next_start_progress
                    print(f"📊 进度更新: {next_start_progress}% (开始执行第 {i+2}/{len(file_list)} 个文件)")
                    test_execution_status['logs'].append(f"📊 进度更新: {next_start_progress}% (开始执行第 {i+2}/{len(file_list)} 个文件)")
                    print(f"⚡ 立即开始执行下一个文件，无延迟...")
                else:
                    print(f"🏁 所有文件执行完成！")
                    test_execution_status['logs'].append(f"🏁 所有文件执行完成！")

            # 确保最终进度为100%
            test_execution_status['progress'] = 100
            test_execution_status['end_time'] = datetime.now()

            # 获取最终批量统计
            batch_summary = get_batch_summary()
            test_execution_status['batch_statistics'] = batch_summary

            # 记录最终统计日志
            test_execution_status['logs'].append("📊 最终进度: 100% (批量执行完成)")
            test_execution_status['logs'].append(f"📈 批量执行统计: 共{batch_summary['total_files']}个文件, {batch_summary['total_tests']}个测试")
            test_execution_status['logs'].append(f"   ✅ 通过: {batch_summary['passed_tests']}个")
            test_execution_status['logs'].append(f"   ❌ 失败: {batch_summary['failed_tests']}个")
            test_execution_status['logs'].append(f"   ⏭️ 跳过: {batch_summary['skipped_tests']}个")
            test_execution_status['logs'].append(f"   🎯 成功率: {batch_summary['success_rate']}%")

            print(f"📈 批量执行最终统计:")
            print(f"   文件: {batch_summary['completed_files']}/{batch_summary['total_files']}")
            print(f"   测试: {batch_summary['total_tests']}个 (通过{batch_summary['passed_tests']}, 失败{batch_summary['failed_tests']}, 跳过{batch_summary['skipped_tests']})")
            print(f"   成功率: {batch_summary['success_rate']}%")

        except Exception as e:
            test_execution_status['logs'].append(f"❌ 批量执行出错: {str(e)}")
            test_execution_status['progress'] = 100  # 即使出错也设置为100%表示结束
            test_execution_status['end_time'] = datetime.now()

        finally:
            test_execution_status['running'] = False
            test_execution_status['current_test'] = None
            self.current_process = None
            test_execution_status['logs'].append("🏁 批量执行流程结束")

    def execute_all_tests(self):
        """执行所有测试文件"""
        global test_execution_status

        test_execution_status['running'] = True
        test_execution_status['start_time'] = datetime.now()
        test_execution_status['progress'] = 0
        test_execution_status['results'] = []
        test_execution_status['logs'] = []
        reset_stop_flag()  # 重置停止标志

        try:
            test_execution_status['logs'].append("开始执行所有测试文件")

            # 检查是否请求停止
            if is_stop_requested():
                test_execution_status['logs'].append("🛑 检测到停止请求，取消执行")
                return

            # 构建pytest命令，执行TestCases目录下的所有测试
            cmd = [
                sys.executable, '-m', 'pytest',
                cases_dir,
                '-v', '--tb=short', '--no-header',
                f'--alluredir={reports_dir}/allure-results'
            ]

            test_execution_status['progress'] = 10
            test_execution_status['current_test'] = "执行所有测试"

            # 执行测试
            self.current_process = subprocess.Popen(
                cmd,
                cwd=project_root,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                encoding='utf-8'
            )

            # 添加进程到管理列表
            add_process(self.current_process)

            try:
                # 检查停止请求的同时等待进程完成
                while self.current_process.poll() is None:
                    if is_stop_requested():
                        test_execution_status['logs'].append("🛑 收到停止请求，正在终止所有测试...")
                        self.current_process.terminate()
                        try:
                            self.current_process.wait(timeout=5)
                        except subprocess.TimeoutExpired:
                            self.current_process.kill()
                            self.current_process.wait()
                        test_execution_status['logs'].append("✅ 所有测试进程已终止")
                        return
                    time.sleep(0.1)

                stdout, stderr = self.current_process.communicate()
            finally:
                # 从管理列表移除进程
                remove_process(self.current_process)

            test_execution_status['progress'] = 100
            test_execution_status['end_time'] = datetime.now()

            # 使用pytest解析器解析详细结果
            test_results, statistics = pytest_parser.parse_output(stdout, stderr)
            duration = (test_execution_status['end_time'] - test_execution_status['start_time']).total_seconds()

            # 转换为前端格式
            frontend_results = []
            for result in test_results:
                frontend_results.append({
                    'test': result.test_name,
                    'status': result.status,
                    'output': stdout[:500] if stdout else "",
                    'error': result.error_message or stderr,
                    'duration': result.duration,
                    'file_path': result.file_path,
                    'class_name': result.class_name,
                    'method_name': result.method_name
                })

            # 如果没有解析到具体结果，使用整体结果
            if not frontend_results:
                success = self.current_process.returncode == 0
                frontend_results = [{
                    'test': '所有测试',
                    'status': 'PASSED' if success else 'FAILED',
                    'output': stdout,
                    'error': stderr,
                    'duration': duration
                }]

            test_execution_status['results'] = frontend_results
            test_execution_status['statistics'] = statistics

            # 添加每个测试结果到会话管理器
            for result in test_results:
                session_manager.add_test_result(
                    test_name=result.test_name,
                    status=result.status,
                    duration=result.duration,
                    error_message=result.error_message if result.error_message else None,
                    output=stdout[:200] if stdout else None
                )

            test_execution_status['logs'].append("所有测试执行完成")
            if stdout:
                test_execution_status['logs'].append("=== 输出 ===")
                test_execution_status['logs'].append(stdout)
            if stderr:
                test_execution_status['logs'].append("=== 错误 ===")
                test_execution_status['logs'].append(stderr)

        except Exception as e:
            test_execution_status['logs'].append(f"执行出错: {str(e)}")

        finally:
            test_execution_status['running'] = False
            test_execution_status['current_test'] = None
            self.current_process = None

# 创建测试执行器实例
test_executor = TestExecutor()

def initialize_application():
    """初始化应用状态"""
    global test_execution_status

    print("🚀 初始化应用状态...")

    # 终止任何可能残留的进程
    killed_count = kill_all_processes()
    if killed_count > 0:
        print(f"⚡ 清理了 {killed_count} 个残留进程")

    # 确保状态正确初始化
    test_execution_status = {
        'running': False,
        'current_test': None,
        'progress': 0,
        'total_tests': 0,
        'results': [],
        'start_time': None,
        'end_time': None,
        'logs': [],  # 初始化时不添加任何日志消息，避免前端轮询时重复显示
        'stop_requested': False
    }

    # 重置测试执行器状态
    test_executor.current_process = None

    print("✅ 应用状态初始化完成")

# 初始化应用
initialize_application()

@app.route('/debug-template')
def debug_template():
    """调试模板内容"""
    try:
        with open('templates/simple_index.html', 'r', encoding='utf-8') as f:
            content = f.read()

        # 查找分页容器
        import re
        match = re.search(r'<div id="paginationContainer"[^>]*>', content)
        if match:
            return f"模板中的分页容器: {match.group(0)}"
        else:
            return "未找到分页容器"
    except Exception as e:
        return f"错误: {str(e)}"

@app.route('/')
def index():
    """主页"""
    try:
        # 强制重新加载模板
        app.jinja_env.cache = {}
        return render_template('simple_index.html')
    except Exception as e:
        print(f"模板渲染错误: {str(e)}")
        return f"模板渲染错误: {str(e)}", 500



@app.route('/sessions')
def sessions_page():
    """会话管理页面"""
    try:
        return render_template('sessions.html')
    except Exception as e:
        return f"模板错误: {str(e)}"







@app.route('/api/test-cases')
def get_test_cases():
    """获取所有测试用例"""
    try:
        print("开始获取测试用例...")
        discovery = TestCaseDiscovery()
        test_cases = discovery.get_all_test_cases()
        print(f"发现 {len(test_cases)} 个测试用例")
        return jsonify({
            'success': True,
            'test_cases': test_cases,
            'total': len(test_cases)
        })
    except Exception as e:
        print(f"获取测试用例出错: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/execute-test', methods=['POST'])
def execute_test():
    """执行测试用例"""
    try:
        print("=" * 50)
        print("🚀 收到测试执行请求")

        data = request.get_json()
        print(f"📝 请求数据: {data}")

        file_path = data.get('file_path')
        test_name = data.get('test_name')
        file_list = data.get('file_list', [])  # 支持多文件执行

        print(f"📁 文件路径: {file_path}")
        print(f"🧪 测试名称: {test_name}")
        print(f"📋 文件列表: {file_list}")

        if not file_path and not file_list:
            print("❌ 缺少必要参数")
            return jsonify({
                'success': False,
                'error': '缺少文件路径或文件列表参数'
            }), 400

        # 检查文件是否存在
        if file_path:
            if os.path.exists(file_path):
                print(f"✅ 文件存在: {file_path}")
            else:
                print(f"❌ 文件不存在: {file_path}")
                return jsonify({
                    'success': False,
                    'error': f'文件不存在: {file_path}'
                }), 400

        # 使用锁确保并发控制
        with execution_lock:
            if test_execution_status['running']:
                print("⚠️  已有测试在执行中")
                return jsonify({
                    'success': False,
                    'error': '已有测试正在执行中'
                }), 409

            # 立即设置运行状态，防止并发请求
            test_execution_status['running'] = True
            test_execution_status['start_time'] = datetime.now()
            test_execution_status['end_time'] = None
            print("🔒 已锁定执行状态，防止并发执行")

            # 强制终止任何可能存在的旧线程（不使用锁，因为当前已在锁内）
            print("🔍 开始清理旧进程...")
            try:
                killed_count = kill_all_processes_no_lock()
                print(f"🧹 已清理 {killed_count} 个旧进程")
            except Exception as e:
                print(f"❌ 清理进程时出错: {e}")
                import traceback
                traceback.print_exc()

        print("🎯 准备启动测试执行线程...")

        # 生成唯一的执行请求ID
        import uuid
        global execution_request_id, current_execution_thread
        execution_request_id = str(uuid.uuid4())[:8]
        print(f"🆔 执行请求ID: {execution_request_id}")

        # 在后台线程中执行测试
        if file_list:
            print(f"📦 批量执行 {len(file_list)} 个文件")
            # 批量执行多个文件
            thread = threading.Thread(
                target=test_executor.execute_multiple_files,
                args=(file_list, execution_request_id),
                name=f"BatchExecution-{execution_request_id}"
            )
        else:
            print(f"📄 执行单个文件: {file_path}")
            # 执行单个文件或测试
            thread = threading.Thread(
                target=test_executor.execute_test,
                args=(file_path, test_name, execution_request_id),
                name=f"SingleExecution-{execution_request_id}"
            )

        thread.daemon = True
        current_execution_thread = thread
        thread.start()

        print(f"🚀 线程已启动: {thread.name}")
        print(f"🔍 当前活跃线程数: {threading.active_count()}")
        print(f"🎯 执行线程ID: {thread.ident}")

        print("✅ 测试执行线程已启动")
        print("=" * 50)

        return jsonify({
            'success': True,
            'message': '测试开始执行'
        })

    except Exception as e:
        print(f"API执行出错: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/execute-all', methods=['POST'])
def execute_all_tests():
    """执行所有测试文件"""
    try:
        if test_execution_status['running']:
            return jsonify({
                'success': False,
                'error': '已有测试正在执行中'
            }), 409

        # 在后台线程中执行所有测试
        thread = threading.Thread(
            target=test_executor.execute_all_tests
        )
        thread.daemon = True
        thread.start()

        return jsonify({
            'success': True,
            'message': '开始执行所有测试'
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/stop-execution', methods=['POST'])
def stop_execution():
    """停止测试执行"""
    try:
        print("🛑 收到停止执行请求")

        if not test_execution_status['running']:
            print("⚠️ 当前没有正在执行的测试")
            return jsonify({
                'success': False,
                'message': '当前没有正在执行的测试'
            }), 400

        # 设置停止标志
        test_execution_status['stop_requested'] = True
        print("🚩 已设置停止标志")

        # 终止所有当前进程
        killed_count = kill_all_processes()
        print(f"⚡ 已终止 {killed_count} 个进程")

        # 重置执行状态
        test_execution_status['running'] = False
        test_execution_status['current_test'] = None
        test_execution_status['end_time'] = datetime.now()

        # 添加停止日志
        test_execution_status['logs'].append("🛑 用户请求停止执行")
        test_execution_status['logs'].append(f"✅ 已终止 {killed_count} 个进程")
        test_execution_status['logs'].append("📊 执行已停止")

        print("✅ 停止执行完成")

        return jsonify({
            'success': True,
            'message': f'测试执行已停止，终止了 {killed_count} 个进程',
            'killed_processes': killed_count
        })

    except Exception as e:
        print(f"❌ 停止执行失败: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/emergency-stop', methods=['POST'])
def emergency_stop_all():
    """紧急停止所有执行 - 最强力的停止方法"""
    try:
        print("🚨 收到紧急停止请求")

        # 强制设置停止标志
        test_execution_status['stop_requested'] = True

        # 终止所有进程
        killed_count = kill_all_processes()

        # 强制终止所有线程（通过设置daemon=True让它们自动退出）
        active_threads = threading.active_count()
        print(f"🧵 当前活跃线程数: {active_threads}")

        # 强制重置状态
        with execution_lock:
            test_execution_status['running'] = False
            test_execution_status['current_test'] = None
            test_execution_status['progress'] = 100
            test_execution_status['end_time'] = datetime.now()
            test_execution_status['logs'].append("🚨 紧急停止：所有执行已强制终止")

        print("✅ 紧急停止完成")

        return jsonify({
            'success': True,
            'message': f'紧急停止完成，终止了 {killed_count} 个进程',
            'killed_processes': killed_count,
            'active_threads': active_threads
        })

    except Exception as e:
        print(f"❌ 紧急停止失败: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/reset-status', methods=['POST'])
def reset_execution_status():
    """强制重置执行状态"""
    try:
        print("🔄 收到强制重置状态请求")

        # 终止所有当前进程
        killed_count = kill_all_processes()
        print(f"⚡ 已终止 {killed_count} 个进程")

        # 强制重置所有状态
        global test_execution_status, execution_request_id, current_execution_thread
        test_execution_status = {
            'running': False,
            'current_test': None,
            'progress': 0,
            'total_tests': 0,
            'results': [],
            'start_time': None,
            'end_time': None,
            'logs': [f"🔄 系统状态已强制重置 (终止了 {killed_count} 个进程)"],
            'stop_requested': False,
            'batch_statistics': None,  # 清理批量统计
            'detailed_results': None   # 清理详细结果
        }

        # 重置执行相关的全局变量
        execution_request_id = None
        current_execution_thread = None

        # 重置测试执行器状态
        test_executor.current_process = None

        # 重置批量统计
        reset_batch_statistics()
        print("📊 已重置批量统计数据")

        print("✅ 状态重置完成")

        return jsonify({
            'success': True,
            'message': f'执行状态已强制重置，终止了 {killed_count} 个进程',
            'killed_processes': killed_count
        })

    except Exception as e:
        print(f"❌ 重置状态失败: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/test-status')
def get_test_status():
    """获取测试执行状态"""
    status = test_execution_status.copy()

    # 如果正在执行批量测试，添加实时批量统计
    if status.get('running') and status.get('total_tests', 0) > 1:
        try:
            batch_stats = get_batch_statistics()
            status['batch_statistics'] = get_batch_summary()
            status['detailed_results'] = batch_stats.get('file_results', [])
        except:
            pass  # 如果获取统计失败，不影响基本状态返回

    return jsonify(status)

@app.route('/api/pytest-health')
def check_pytest_health():
    """检查pytest健康状态"""
    try:
        health_info = {
            'pytest_available': False,
            'pytest_version': None,
            'allure_available': False,
            'allure_version': None,
            'compatibility_issues': [],
            'recommendations': []
        }

        # 检查pytest
        try:
            import pytest
            health_info['pytest_available'] = True
            health_info['pytest_version'] = pytest.__version__
        except ImportError:
            health_info['compatibility_issues'].append('pytest未安装')
            health_info['recommendations'].append('运行: pip install pytest==7.4.2')

        # 检查allure
        try:
            import allure_pytest
            health_info['allure_available'] = True
            try:
                health_info['allure_version'] = allure_pytest.__version__
            except AttributeError:
                health_info['allure_version'] = 'unknown'
        except ImportError:
            health_info['recommendations'].append('可选: pip install allure-pytest==2.13.2')

        # 快速测试pytest
        if health_info['pytest_available']:
            try:
                import tempfile
                import os

                # 创建临时测试文件
                with tempfile.NamedTemporaryFile(mode='w', suffix='.py', delete=False) as f:
                    f.write('def test_health(): assert True')
                    temp_file = f.name

                # 运行pytest
                result = subprocess.run([
                    sys.executable, '-m', 'pytest', temp_file, '-v'
                ], capture_output=True, text=True, timeout=10)

                if result.returncode != 0:
                    if "cannot import name 'FixtureDef'" in result.stderr:
                        health_info['compatibility_issues'].append('pytest版本兼容性问题')
                        health_info['recommendations'].append('运行: python fix_pytest.py')
                    else:
                        health_info['compatibility_issues'].append('pytest执行失败')

                # 清理临时文件
                os.unlink(temp_file)

            except Exception as e:
                health_info['compatibility_issues'].append(f'pytest测试失败: {str(e)}')

        return jsonify({
            'success': True,
            'health': health_info
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

# 全局变量存储报告服务器进程
allure_server_process = None
allure_server_port = 8080

def generate_simple_html_report(allure_results_dir):
    """生成简单的HTML报告（当Allure不可用时）"""
    try:
        import json
        import glob
        from datetime import datetime

        # 读取测试结果文件
        result_files = glob.glob(os.path.join(allure_results_dir, '*-result.json'))

        if not result_files:
            return jsonify({
                'success': False,
                'error': '没有找到测试结果文件'
            }), 400

        # 解析测试结果
        all_results = []
        for file_path in result_files:
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    result = json.load(f)
                    all_results.append(result)
            except Exception as e:
                print(f"⚠️ 解析结果文件失败: {file_path}, {e}")

        # 生成HTML报告
        html_content = generate_html_report_content(all_results)

        # 保存HTML报告
        report_dir = os.path.join(reports_dir, 'simple-report')
        os.makedirs(report_dir, exist_ok=True)

        report_file = os.path.join(report_dir, 'index.html')
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(html_content)

        print(f"✅ 简单HTML报告生成成功: {report_file}")

        return jsonify({
            'success': True,
            'message': '简单HTML报告生成成功',
            'report_path': report_dir,
            'report_file': report_file,
            'static_report': True,
            'simple_report': True
        })

    except Exception as e:
        print(f"❌ 简单报告生成失败: {e}")
        return jsonify({
            'success': False,
            'error': f'简单报告生成失败: {str(e)}'
        }), 500

def generate_html_report_content(results):
    """生成HTML报告内容"""
    total_tests = len(results)
    passed_tests = sum(1 for r in results if r.get('status') == 'passed')
    failed_tests = sum(1 for r in results if r.get('status') == 'failed')
    skipped_tests = sum(1 for r in results if r.get('status') == 'skipped')

    success_rate = (passed_tests / total_tests * 100) if total_tests > 0 else 0

    html = f"""
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>黑湖科技自动化测试报告</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        .status-passed {{ color: #28a745; }}
        .status-failed {{ color: #dc3545; }}
        .status-skipped {{ color: #ffc107; }}
        .summary-card {{ border-left: 4px solid #007bff; }}
    </style>
</head>
<body>
    <div class="container mt-4">
        <div class="row">
            <div class="col-12">
                <h1 class="mb-4">
                    <i class="bi bi-graph-up"></i> 黑湖科技自动化测试报告
                </h1>

                <!-- 统计摘要 -->
                <div class="row mb-4">
                    <div class="col-md-3">
                        <div class="card summary-card">
                            <div class="card-body text-center">
                                <h5 class="card-title">总测试数</h5>
                                <h2 class="text-primary">{total_tests}</h2>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card">
                            <div class="card-body text-center">
                                <h5 class="card-title">通过</h5>
                                <h2 class="text-success">{passed_tests}</h2>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card">
                            <div class="card-body text-center">
                                <h5 class="card-title">失败</h5>
                                <h2 class="text-danger">{failed_tests}</h2>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card">
                            <div class="card-body text-center">
                                <h5 class="card-title">成功率</h5>
                                <h2 class="text-info">{success_rate:.1f}%</h2>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 测试结果详情 -->
                <div class="card">
                    <div class="card-header">
                        <h5><i class="bi bi-list-check"></i> 测试结果详情</h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>测试名称</th>
                                        <th>状态</th>
                                        <th>执行时间</th>
                                        <th>错误信息</th>
                                    </tr>
                                </thead>
                                <tbody>
    """

    # 添加测试结果行
    for result in results:
        name = result.get('name', '未知测试')
        status = result.get('status', 'unknown')
        duration = result.get('stop', 0) - result.get('start', 0)
        duration_ms = duration / 1000000 if duration > 0 else 0  # 转换为毫秒

        status_class = f"status-{status}"
        status_icon = {
            'passed': 'bi-check-circle-fill',
            'failed': 'bi-x-circle-fill',
            'skipped': 'bi-dash-circle-fill'
        }.get(status, 'bi-question-circle-fill')

        error_msg = ""
        if status == 'failed' and 'statusDetails' in result:
            error_msg = result['statusDetails'].get('message', '')[:100] + "..." if len(result['statusDetails'].get('message', '')) > 100 else result['statusDetails'].get('message', '')

        html += f"""
                                    <tr>
                                        <td>{name}</td>
                                        <td><i class="bi {status_icon} {status_class}"></i> {status.upper()}</td>
                                        <td>{duration_ms:.0f}ms</td>
                                        <td>{error_msg}</td>
                                    </tr>
        """

    html += f"""
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <div class="mt-4 text-center text-muted">
                    <small>报告生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</small>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
    """

    return html

@app.route('/api/generate-report')
def generate_allure_report():
    """生成并启动Allure报告服务器"""
    global allure_server_process, allure_server_port

    try:
        allure_results_dir = os.path.join(reports_dir, 'allure-results')
        allure_report_dir = os.path.join(reports_dir, 'allure-report')

        if not os.path.exists(allure_results_dir) or not os.listdir(allure_results_dir):
            return jsonify({
                'success': False,
                'error': '没有找到测试结果文件，请先执行测试'
            }), 400

        # 检查是否已安装allure
        allure_available = True
        try:
            subprocess.run(['allure', '--version'], capture_output=True, text=True, timeout=5)
        except FileNotFoundError:
            allure_available = False
            print("⚠️ 未找到allure命令，将生成简单HTML报告")

        if not allure_available:
            # 生成简单的HTML报告
            return generate_simple_html_report(allure_results_dir)

        # 停止现有的报告服务器
        if allure_server_process and allure_server_process.poll() is None:
            try:
                allure_server_process.terminate()
                allure_server_process.wait(timeout=5)
            except:
                try:
                    allure_server_process.kill()
                except:
                    pass

        # 生成Allure报告
        print(f"🔄 正在生成Allure报告...")
        generate_cmd = ['allure', 'generate', allure_results_dir, '-o', allure_report_dir, '--clean']

        result = subprocess.run(generate_cmd, capture_output=True, text=True, timeout=60)
        if result.returncode != 0:
            return jsonify({
                'success': False,
                'error': f'报告生成失败: {result.stderr}'
            }), 500

        print(f"✅ Allure报告生成成功")

        # 启动Allure报告服务器
        print(f"🚀 正在启动Allure报告服务器，端口: {allure_server_port}")
        serve_cmd = ['allure', 'serve', allure_results_dir, '--port', str(allure_server_port)]

        allure_server_process = subprocess.Popen(
            serve_cmd,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True
        )

        # 等待服务器启动
        import time
        time.sleep(3)

        # 检查服务器是否启动成功
        if allure_server_process.poll() is None:
            report_url = f"http://localhost:{allure_server_port}"
            print(f"✅ Allure报告服务器启动成功: {report_url}")

            return jsonify({
                'success': True,
                'message': '报告生成并启动成功',
                'report_url': report_url,
                'port': allure_server_port
            })
        else:
            # 服务器启动失败，尝试生成静态报告
            print(f"⚠️ Allure服务器启动失败，生成静态报告")
            return jsonify({
                'success': True,
                'message': '报告生成成功（静态版本）',
                'report_path': allure_report_dir,
                'static_report': True
            })

    except Exception as e:
        print(f"❌ 报告生成异常: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/stop-report-server')
def stop_report_server():
    """停止Allure报告服务器"""
    global allure_server_process

    try:
        if allure_server_process and allure_server_process.poll() is None:
            allure_server_process.terminate()
            allure_server_process.wait(timeout=5)
            print("✅ Allure报告服务器已停止")
            return jsonify({
                'success': True,
                'message': '报告服务器已停止'
            })
        else:
            return jsonify({
                'success': True,
                'message': '报告服务器未运行'
            })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/session-history')
def get_session_history():
    """获取会话历史"""
    try:
        limit = request.args.get('limit', 10, type=int)
        sessions = session_manager.get_session_history(limit=limit)

        return jsonify({
            'success': True,
            'sessions': sessions,
            'total': len(sessions)
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/session-statistics')
def get_session_statistics():
    """获取会话统计信息"""
    try:
        stats = session_manager.get_session_statistics()

        return jsonify({
            'success': True,
            'statistics': stats
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/current-session')
def get_current_session():
    """获取当前会话信息"""
    try:
        current_session = session_manager.get_current_session()

        if current_session:
            session_data = {
                'session_id': current_session.session_id,
                'start_time': current_session.start_time.isoformat(),
                'execution_type': current_session.execution_type,
                'files_executed': current_session.files_executed,
                'total_tests': current_session.total_tests,
                'passed_tests': current_session.passed_tests,
                'failed_tests': current_session.failed_tests,
                'error_tests': current_session.error_tests
            }
        else:
            session_data = None

        return jsonify({
            'success': True,
            'current_session': session_data
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/reset-ui-state', methods=['POST'])
def reset_ui_state():
    """强制重置UI状态 - 用于修复前端按钮卡住问题"""
    try:
        # 确保后端状态正确
        test_execution_status['running'] = False
        test_execution_status['current_test'] = None

        # 如果没有进度，设置为100%表示完成
        if test_execution_status.get('progress', 0) == 0:
            test_execution_status['progress'] = 100

        # 添加重置日志
        test_execution_status['logs'].append("🔄 UI状态已强制重置")

        return jsonify({
            'success': True,
            'message': 'UI状态已重置',
            'current_status': {
                'running': test_execution_status['running'],
                'progress': test_execution_status['progress'],
                'current_test': test_execution_status['current_test']
            }
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

def main():
    """主函数 - 启动Web应用"""
    # 确保必要的目录存在
    os.makedirs(reports_dir, exist_ok=True)
    os.makedirs(os.path.join(reports_dir, 'allure-results'), exist_ok=True)

    print("🚀 启动BlackLake测试框架 - 简化版Web控制台")
    print(f"测试用例目录: {cases_dir}")
    print(f"报告目录: {reports_dir}")
    print("访问地址: http://localhost:5000")
    print("按 Ctrl+C 停止应用")
    print()

    try:
        app.run(debug=False, host='0.0.0.0', port=5000, threaded=True)
    except KeyboardInterrupt:
        print("\n👋 Web应用已停止")
    except Exception as e:
        print(f"\n❌ 启动失败: {str(e)}")

if __name__ == '__main__':
    main()
