import os
import sys
import pytest
from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from webdriver_manager.chrome import ChromeDriverManager

from TestDatas import global_datas as g_data
from PageObjects.login_page import LoginPage
from Common.my_logger import logger
from Common.handle_path import conf_dir
from Common.driver_manager import get_platform_driver
from Common.platform_utils import get_platform_info


def get_chrome_options():
    """
    获取Chrome浏览器选项配置（跨平台兼容）

    Returns:
        Options: Chrome浏览器选项对象
    """
    chrome_options = Options()

    # 基础选项配置
    chrome_options.add_argument('--no-sandbox')
    chrome_options.add_argument('--disable-dev-shm-usage')
    chrome_options.add_argument('--disable-gpu')
    chrome_options.add_argument('--disable-extensions')

    # 根据平台添加特定选项
    if sys.platform == 'darwin':  # Mac
        logger.info("检测到Mac平台，应用Mac专用Chrome选项")
        chrome_options.add_argument('--disable-web-security')
        chrome_options.add_argument('--allow-running-insecure-content')
    elif sys.platform.startswith('win'):  # Windows
        logger.info("检测到Windows平台，应用Windows专用Chrome选项")
        chrome_options.add_argument('--disable-features=VizDisplayCompositor')
    else:  # Linux
        logger.info("检测到Linux平台，应用Linux专用Chrome选项")
        chrome_options.add_argument('--headless')  # Linux环境可能需要无头模式

    return chrome_options


def get_chrome_driver():
    """
    获取Chrome WebDriver实例（跨平台兼容）

    Returns:
        webdriver.Chrome: Chrome WebDriver实例
    """
    try:
        logger.info(f"当前运行平台: {sys.platform}")

        # 使用webdriver-manager自动管理驱动程序
        # 这会自动下载适合当前平台的chromedriver
        service = Service(ChromeDriverManager().install())

        # 获取平台特定的Chrome选项
        chrome_options = get_chrome_options()

        # 创建WebDriver实例
        driver = webdriver.Chrome(service=service, options=chrome_options)

        logger.info("Chrome WebDriver初始化成功")
        return driver

    except Exception as e:
        logger.error(f"Chrome WebDriver初始化失败: {str(e)}")
        raise


#定义fixture - 前置后置 -作用域 -返回值
@pytest.fixture(scope="class")
def init():
    """
    测试初始化fixture（跨平台兼容）
    """
    logger.info("=========class级 前置操作：打开浏览器，访问登录页面=========")

    # 记录平台信息
    platform_info = get_platform_info()
    logger.info(f"运行平台: {platform_info['platform_type']}")
    logger.debug(f"详细平台信息: {platform_info}")

    # 使用跨平台兼容的WebDriver初始化
    driver = get_platform_driver('chrome')

    # 最大化窗口
    driver.maximize_window()

    # 访问登录页面
    driver.get(g_data.login_url)

    yield driver

    logger.info("=========class级 后置操作：关闭浏览器，退出会话=========")
    driver.quit()

# 前置条件：用户已登陆   后置条件：关闭浏览器
@pytest.fixture
def back_login(init):
    init.get(g_data.login_url)
    yield init


# 前置条件：用户已登陆   后置条件：关闭浏览器
@pytest.fixture(scope="class")
def access_base(init):
    logger.info("=========class级 前置操作：登陆系统=========")
    LoginPage(init).login(*g_data.account_user)
    yield init

