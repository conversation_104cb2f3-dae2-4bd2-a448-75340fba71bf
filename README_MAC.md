# BlackLake测试框架 - Mac版本

## 概述

BlackLake测试框架现已完全支持Mac平台！本文档将指导您在Mac系统上安装和使用该测试框架。

## 系统要求

- **操作系统**: macOS 10.14 或更高版本
- **Python**: 3.8 或更高版本
- **浏览器**: Google Chrome（推荐）或 Firefox
- **内存**: 至少 4GB RAM
- **存储**: 至少 2GB 可用空间

## 快速安装

### 方法一：自动安装（推荐）

1. 克隆或下载项目到本地
2. 打开终端，进入项目目录
3. 运行安装脚本：

```bash
chmod +x setup_mac.sh
./setup_mac.sh
```

安装脚本将自动完成以下操作：
- 检查系统环境
- 安装必要的依赖（如Homebrew、Chrome等）
- 创建Python虚拟环境
- 安装所有必需的Python包
- 配置Mac专用设置
- 创建启动脚本

### 方法二：手动安装

1. **安装Python 3.8+**
   ```bash
   # 使用Homebrew安装（推荐）
   brew install python
   
   # 或从官网下载安装包
   # https://www.python.org/downloads/
   ```

2. **安装Chrome浏览器**
   ```bash
   brew install --cask google-chrome
   ```

3. **创建虚拟环境**
   ```bash
   python3 -m venv venv
   source venv/bin/activate
   ```

4. **安装依赖包**
   ```bash
   pip install -r requirements_mac.txt
   ```

## 使用方法

### 运行测试

```bash
# 使用便捷脚本
./run_tests_mac.sh

# 或手动运行
source venv/bin/activate
python -m pytest TestCases/ --alluredir=./allure-results -v
```

### 启动Web界面

```bash
# 使用便捷脚本
./run_web_app_mac.sh

# 或手动运行
source venv/bin/activate
python web_app.py
```

然后在浏览器中访问 `http://localhost:5000`

### 生成测试报告

```bash
# 安装Allure（如果尚未安装）
brew install allure

# 生成报告
allure generate ./allure-results -o ./allure-report --clean
allure open ./allure-report
```

## 配置说明

### Mac专用配置文件

- **位置**: `Conf/config_mac.ini`
- **说明**: 包含Mac平台的专用配置选项

主要配置项：
```ini
[browser]
default_browser = chrome
headless = False
window_size = 1920,1080

[chrome_options]
disable_web_security = True
allow_running_insecure_content = True
no_sandbox = True
```

### 环境变量

可以通过环境变量覆盖默认配置：

```bash
export BLACKLAKE_BROWSER=chrome
export BLACKLAKE_HEADLESS=false
export BLACKLAKE_LOG_LEVEL=INFO
```

## 跨平台特性

### 自动平台检测

框架会自动检测运行平台并应用相应配置：

```python
from Common.platform_utils import get_platform, is_mac

if is_mac():
    print("运行在Mac平台")
    # Mac专用逻辑
```

### 智能驱动管理

使用`webdriver-manager`自动下载和管理浏览器驱动：

```python
from Common.driver_manager import get_platform_driver

# 自动获取适合当前平台的Chrome驱动
driver = get_platform_driver('chrome')
```

### 跨平台键盘快捷键

自动适配不同平台的键盘快捷键：

```python
from Common.platform_utils import KeyboardUtils

# Mac: Cmd+A, Windows/Linux: Ctrl+A
select_all_key = KeyboardUtils.get_select_all_key()
```

## 常见问题

### Q: 安装时提示权限错误
A: 确保有管理员权限，或使用 `sudo` 运行安装脚本

### Q: Chrome驱动下载失败
A: 检查网络连接，或手动设置代理：
```bash
export HTTP_PROXY=http://your-proxy:port
export HTTPS_PROXY=http://your-proxy:port
```

### Q: 测试运行时浏览器无法启动
A: 检查Chrome是否正确安装，路径是否正确：
```bash
/Applications/Google Chrome.app/Contents/MacOS/Google Chrome --version
```

### Q: 无法访问某些网站
A: 检查系统安全设置，可能需要在"安全性与隐私"中允许Chrome访问

### Q: 截图功能异常
A: 确保有屏幕录制权限：
系统偏好设置 → 安全性与隐私 → 隐私 → 屏幕录制

## 目录结构

```
BlackLake/
├── Common/                 # 公共模块
│   ├── driver_manager.py  # 跨平台驱动管理
│   ├── platform_utils.py  # 平台工具
│   └── basepage.py        # 基础页面类
├── Conf/                  # 配置文件
│   ├── config_mac.ini     # Mac专用配置
│   └── config_xgd.ini     # 通用配置
├── TestCases/             # 测试用例
├── requirements_mac.txt   # Mac依赖包
├── setup_mac.sh          # Mac安装脚本
├── run_tests_mac.sh      # Mac测试运行脚本
└── run_web_app_mac.sh    # Mac Web应用启动脚本
```

## 性能优化建议

### Mac平台特定优化

1. **内存管理**
   ```bash
   # 增加虚拟内存（如果需要）
   sudo sysctl -w vm.swappiness=10
   ```

2. **Chrome性能优化**
   - 关闭不必要的Chrome扩展
   - 使用无头模式进行批量测试
   - 定期清理Chrome缓存

3. **并发测试**
   ```bash
   # 并行运行测试（谨慎使用）
   python -m pytest TestCases/ -n 4 --alluredir=./allure-results
   ```

## 更新日志

### v1.0.0 (Mac兼容版)
- ✅ 完全支持Mac平台
- ✅ 自动平台检测和配置
- ✅ 跨平台驱动管理
- ✅ Mac专用安装脚本
- ✅ 智能键盘快捷键适配
- ✅ 优化的Chrome选项配置
- ✅ 增强的错误处理和日志记录
- ✅ 自动化安装和配置流程

## 技术支持

如遇到问题，请按以下步骤排查：

1. **查看日志文件**
   ```bash
   tail -f Outputs/logs/blacklake_mac.log
   ```

2. **检查系统兼容性**
   ```bash
   python -c "from Common.platform_utils import get_platform_info; print(get_platform_info())"
   ```

3. **验证驱动程序**
   ```bash
   python -c "from Common.driver_manager import get_platform_driver; driver = get_platform_driver('chrome'); print('驱动正常'); driver.quit()"
   ```

4. **提交Issue**
   - 包含完整的错误日志
   - 说明系统环境信息
   - 描述重现步骤

## 贡献指南

欢迎贡献代码来改进跨平台兼容性！

### 开发环境设置
```bash
# Fork项目后克隆
git clone https://github.com/your-username/BlackLake.git
cd BlackLake

# 安装开发依赖
pip install -r requirements_mac.txt
pip install -r requirements_dev.txt  # 如果存在

# 运行测试
python -m pytest tests/
```

### 提交规范
- 遵循PEP 8代码规范
- 添加适当的测试用例
- 更新相关文档
- 提交前运行完整测试套件

---

**BlackLake Team** | 让自动化测试更简单，跨平台更容易
