
from selenium.webdriver.common.by import By


# 创建工艺路线
class CreateProcessRoutingPageLocs:
    # 工艺路线菜单
    process_routing_loc = (By.XPATH, "//a[contains(text(), '工艺路线')]")

    # 点击创建工艺路线
    create_process_routing_loc  = (By.XPATH,"//span[contains(text(), '创建工艺路线')]")
    # 工艺路线编号
    process_routing_code_loc = (By.XPATH,"//*[@id='model']")

    # 工艺路线工艺路线名称
    process_routing_name_loc = (By.XPATH, "//*[@id='name']")

    # 工艺路线添加一行
    add_line_loc = (By.XPATH, "//span[contains(text(),'添加一行')]")

    # 点击工艺编号下拉框 //*[@id="kodo-select-eleId-1"]
    click_code_loc = (By.CLASS_NAME, "kodo-select-selector")

    # 选择工艺编号  //*[@id="kodo-select-eleId-1"]
    select_code_loc = (By.XPATH, "(//div[contains(@class, 'kodo-select-item-option')])[1]")

    # 点击保存
    click_save_loc = (By.XPATH, "//span[contains(text(), '保存')]")

    create_tip_loc = (By.XPATH, "//span[contains(text(), '创建成功')]")

    # 编辑模块
    # 点击编辑工艺路线
    update_process_routing_loc = (By.XPATH, "//span[contains(text(), '编辑')]")
    # 工艺路线编号
    update_process_routing_code_loc = (By.XPATH, "//*[@id='model']")

    # 工艺路线工艺路线名称
    update_process_routing_name_loc = (By.XPATH, "//*[@id='name']")

    # 选择工艺编号  //*[@id="kodo-select-eleId-1"]
    update_code_loc = (By.XPATH, "(//div[contains(@class, 'kodo-select-item-option')])[2]")

    update_tip_loc = (By.XPATH, "//span[contains(text(), '编辑成功')]")


    # 删除操作
    # 删除工艺路线
    delete_process_routing_loc = (By.XPATH, "//span[contains(text(), '删除')]")

    # 删除确认按钮
    delete_sure_loc = (By.XPATH, "//span[contains(text(), '确 定')]")

    # 删除成功
    delete_success_loc = (By.XPATH, "//span[contains(text(), '删除成功')]")








