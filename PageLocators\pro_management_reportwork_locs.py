
from selenium.webdriver.common.by import By



# 生成管理---> 报工
class ReportWorkPageLocs:
    # -----------------------------------------创建报工-----------------------------------------
    # 报工菜单
    report_work_loc = (By.XPATH, "//a[text()='报工']")
    # 创建报工按钮
    create_report_work_loc  = (By.XPATH,"//span[contains(text(), '创建报工')]")
    # 工单编号
    report_work_product_manage_loc = (By.XPATH, '//*[@id="projectId"]')
    # 产品信息选择第一个
    product_manage_first_loc = (By.XPATH, '(//div[@class="ant-select-item-option-content"])[1]')
    # 生产人员
    work_product_person_loc = (By.XPATH, '//*[@id="user"]')
    # 生产人员选择第一个
    work_product_person_first_loc = (By.XPATH, '//div[@title="系统管理员"]')

    # 点击保存
    create_task_loc = (By.XPATH, "//span[contains(text(), '保存')]")
    # 创建成功提示
    sure_success_loc = (By.XPATH, "//span[contains(text(), '创建成功')]")


    # -----------------------------------------编辑报工-----------------------------------------
    # 编辑报工
    update_report_work_loc = (By.XPATH, "//div[contains(text(), '编辑')]")
    # 编辑报工数
    update_report_work_data_loc = (By.XPATH, '//*[@id="reportAmount"]')
    # 确定
    update_save_loc = (By.XPATH, "//span[contains(text(), '保存')]")
    # 编辑成功提示
    update_success_loc = (By.XPATH, "//span[contains(text(), '报工编辑成功')]")
    # -----------------------------------------删除报工-----------------------------------------
    # 点击操作按钮
    click_operate_loc = (By.XPATH, "(//div[text()='操作'])[2]")
    # 删除报工按钮
    delete_report_work_loc = (By.CSS_SELECTOR, "ul.list-box > li.list-item:last-child a")
    # 确认删除报工按钮
    delete_sure_report_work_loc = (By.CSS_SELECTOR, "button.kodo-new-btn.kodo-new-btn-primary.kodo-new-btn-dangerous")
    # 删除成功提示
    delete_success_loc = (By.XPATH, "//span[contains(text(), '删除成功')]")










