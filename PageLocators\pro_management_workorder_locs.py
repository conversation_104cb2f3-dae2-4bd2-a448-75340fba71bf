
from selenium.webdriver.common.by import By



# 生成管理---> 工单
class WorkOrderPageLocs:
    # -----------------------------------------创建工单-----------------------------------------
    # 工单菜单
    work_order_loc = (By.XPATH, "//a[text()='工单']")
    # 创建工单按钮
    create_work_order_loc  = (By.XPATH,"//span[contains(text(), '创建工单')]")
    # 点击产品
    click_production = (By.XPATH,"(//div[contains(@class,'kodo-select-selector')])[2]")
    # 产品信息创建
    work_order_product_manage_loc = (By.XPATH, "(//input[contains(@id, 'kodo-select-') and @role='combobox'])[last()]")
    # 产品信息选择第一个
    product_manage_first_loc = (By.CSS_SELECTOR, 'div.kodo-select-item-option-active')
    # 计划数
    work_order_plan_data_loc = (By.XPATH, '//*[@ id="amountPlanned"]')
    # 滑动页面
    scroll_page_loc = (By.XPATH, '//*[@id="productionTaskAnchor"]')

    # 点击添加任务
    click_to_add_task_loc = (By.XPATH, "(//button[contains(@class,'kodo-new-btn-secondary')])[1]")
    # 点击创建工序
    create_new_product_loc = (By.XPATH, "//span[text()='创建工序']")
    # 工序名称
    product_name_loc = (By.XPATH, '//*[@id="name"]')
    # 添加任务确定
    create_task_loc = (By.XPATH, "//button[contains(@class, 'kodo-new-btn-primary') and .//span[normalize-space()='确 定']]")
    # 添加任务最终确定
    create_task_last_loc = (By.XPATH, "//button[contains(@class, 'kodo-new-btn-primary') and .//span[normalize-space()='确定']]")
    # 点击最终创建
    sure_success_loc = (By.XPATH, "//button[@type='button']//span[text ()='创建']")
    # 创建成功提示
    get_success_loc = (By.XPATH, "//span[text ()='创建成功']")


    # -----------------------------------------编辑工单-----------------------------------------
    # 编辑工单
    update_work_order_loc = (By.XPATH, "(//div[text ()='编辑'])[1]")
    # 编辑计划数
    update_code_loc = (By.XPATH, '//*[@id="amountPlanned"]')
    # 确定
    update_save_loc = (By.XPATH, "//button[contains(@class, 'kodo-new-btn-primary') and .//span[text()='保存']]")
    # 编辑成功提示
    update_success_loc = (By.XPATH, "//span[text()='保存成功']")
    # -----------------------------------------删除工单-----------------------------------------
    # 点击操作按钮
    click_operation_loc = (By.XPATH, "(//div[@class='action__hvs4Y']//div[text()='操作'])[1]")
    # 点击删除工单按钮
    delete_work_order_loc = (By.XPATH, "//a[contains(text(), '删除')]")
    # 删除工单按钮
    delete_sure_work_order_loc = (By.XPATH, "//span[text()= '删 除']")
    # 删除成功提示
    delete_success_text_loc = (By.XPATH, "//span[text()= '删除成功']")










