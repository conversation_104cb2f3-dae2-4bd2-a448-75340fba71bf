[tool:pytest]
# BlackLake测试框架 - pytest配置文件

# 测试发现配置
testpaths = TestCases
python_files = test_*.py *_test.py atest_*.py
python_classes = Test*
python_functions = test_*

# 排除的文件和目录
norecursedirs = 
    .git
    .venv
    venv
    __pycache__
    .pytest_cache
    build
    dist
    *.egg-info
    Outputs
    static
    templates
    Common
    PageObjects
    PageLocators
    TestDatas
    Conf

# 忽略的文件模式
ignore = 
    web_app.py
    simple_web_app.py
    start_web_app.py
    main.py
    setup_*.py
    run_*.py
    *_manager.py
    brand_config.py
    check_brand_update.py
    session_manager.py
    pytest_parser.py

# 输出配置
addopts = 
    -v
    --tb=short
    --strict-markers
    --disable-warnings
    --color=yes
    --durations=10

# 标记配置
markers =
    slow: 标记为慢速测试
    fast: 标记为快速测试
    smoke: 冒烟测试
    regression: 回归测试
    ui: UI测试
    api: API测试
    integration: 集成测试
    unit: 单元测试
    mac: Mac平台专用测试
    windows: Windows平台专用测试
    linux: Linux平台专用测试

# 最小版本要求
minversion = 6.0

# 测试超时（秒）
timeout = 300

# 并发配置
# 如果安装了pytest-xdist，可以启用并发测试
# addopts = -n auto

# 覆盖率配置（如果安装了pytest-cov）
# addopts = --cov=Common --cov=PageObjects --cov-report=html --cov-report=term

# 日志配置
log_cli = true
log_cli_level = INFO
log_cli_format = %(asctime)s [%(levelname)8s] %(name)s: %(message)s
log_cli_date_format = %Y-%m-%d %H:%M:%S

# 文件日志
log_file = Outputs/logs/pytest.log
log_file_level = DEBUG
log_file_format = %(asctime)s [%(levelname)8s] %(filename)s:%(lineno)d: %(message)s
log_file_date_format = %Y-%m-%d %H:%M:%S

# 自动使用标记
# 如果测试文件名包含特定关键字，自动添加标记
# 例如：test_ui_*.py 自动添加 @pytest.mark.ui
