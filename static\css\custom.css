/* BlackLake测试框架 - Mac优化样式 */

/* 全局样式 - Mac优化 */
body {
    /* Mac系统字体优先级 */
    font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Display', 'SF Pro Text', 'Helvetica Neue', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f5f5f7; /* Mac风格的背景色 */
    -webkit-font-smoothing: antialiased; /* Mac字体平滑 */
    -moz-osx-font-smoothing: grayscale;
    line-height: 1.47059; /* Mac系统标准行高 */
}

/* Mac特定的滚动条样式 */
::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

::-webkit-scrollbar-track {
    background: transparent;
}

::-webkit-scrollbar-thumb {
    background: rgba(0, 0, 0, 0.2);
    border-radius: 4px;
    border: 2px solid transparent;
    background-clip: content-box;
}

::-webkit-scrollbar-thumb:hover {
    background: rgba(0, 0, 0, 0.3);
    background-clip: content-box;
}

/* Mac风格的选择样式 */
::selection {
    background: rgba(0, 122, 255, 0.3); /* Mac蓝色选择 */
    color: inherit;
}

/* 页面标题 - Mac风格 */
.page-title {
    background: linear-gradient(135deg, #007AFF 0%, #5856D6 100%); /* Mac系统蓝色 */
    color: white;
    padding: 24px 0; /* Mac标准间距 */
    margin-bottom: 32px;
    border-radius: 12px; /* Mac圆角标准 */
    text-align: center;
    box-shadow: 0 4px 20px rgba(0, 122, 255, 0.25); /* Mac蓝色阴影 */
    backdrop-filter: blur(20px); /* Mac毛玻璃效果 */
    -webkit-backdrop-filter: blur(20px);
}

/* 卡片样式增强 - Mac风格 */
.card {
    border: 1px solid rgba(0, 0, 0, 0.06); /* Mac细边框 */
    border-radius: 12px; /* Mac标准圆角 */
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1), 0 1px 2px rgba(0, 0, 0, 0.06); /* Mac阴影 */
    transition: all 0.2s cubic-bezier(0.25, 0.46, 0.45, 0.94); /* Mac缓动函数 */
    background: rgba(255, 255, 255, 0.8); /* Mac半透明背景 */
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
}

.card:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15), 0 2px 4px rgba(0, 0, 0, 0.08);
    transform: translateY(-1px); /* Mac微妙的悬停效果 */
    border-color: rgba(0, 122, 255, 0.2);
}

.card-header {
    background: rgba(248, 249, 250, 0.8); /* Mac半透明头部 */
    border-bottom: 1px solid rgba(0, 0, 0, 0.08);
    border-radius: 12px 12px 0 0 !important;
    font-weight: 590; /* Mac字体权重 */
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
}

/* 测试用例项样式 */
.test-case-item {
    border: 1px solid #e9ecef;
    border-radius: 10px;
    margin-bottom: 12px;
    padding: 16px;
    transition: all 0.3s ease;
    background: white;
    position: relative;
    overflow: hidden;
}

.test-case-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
    background: #6c757d;
    transition: all 0.3s ease;
}

.test-case-item:hover {
    box-shadow: 0 6px 20px rgba(0,0,0,0.1);
    transform: translateY(-3px);
    border-color: #0d6efd;
}

.test-case-item:hover::before {
    background: #0d6efd;
    width: 6px;
}

.test-case-item.selected {
    border-color: #0d6efd;
    background: linear-gradient(135deg, #f8f9ff 0%, #e7f3ff 100%);
    box-shadow: 0 4px 15px rgba(13, 110, 253, 0.2);
}

.test-case-item.selected::before {
    background: #0d6efd;
    width: 6px;
}

/* 文件组样式 */
.test-file-group {
    margin-bottom: 25px;
}

.file-header {
    background: linear-gradient(135deg, #e9ecef 0%, #f8f9fa 100%);
    padding: 15px 20px;
    border-radius: 10px;
    margin-bottom: 15px;
    font-weight: 600;
    border-left: 5px solid #6c757d;
    box-shadow: 0 2px 8px rgba(0,0,0,0.05);
}

.test-method {
    margin-left: 25px;
    padding: 12px 16px;
    background: white;
    border-left: 4px solid #6c757d;
    margin-bottom: 8px;
    cursor: pointer;
    border-radius: 8px;
    transition: all 0.3s ease;
    box-shadow: 0 1px 3px rgba(0,0,0,0.05);
}

.test-method:hover {
    background: #f8f9fa;
    border-left-color: #0d6efd;
    transform: translateX(5px);
    box-shadow: 0 3px 10px rgba(0,0,0,0.1);
}

.test-method.selected {
    border-left-color: #0d6efd;
    background: linear-gradient(135deg, #f8f9ff 0%, #e7f3ff 100%);
    box-shadow: 0 3px 12px rgba(13, 110, 253, 0.15);
}

/* 执行控制面板 */
.execution-panel {
    position: sticky;
    top: 20px;
    background: white;
    border: none;
    border-radius: 15px;
    padding: 25px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.1);
    backdrop-filter: blur(10px);
}

/* 按钮样式增强 - Mac风格 */
.btn {
    border-radius: 8px; /* Mac标准圆角 */
    font-weight: 590; /* Mac字体权重 */
    transition: all 0.2s cubic-bezier(0.25, 0.46, 0.45, 0.94); /* Mac缓动 */
    position: relative;
    overflow: hidden;
    font-size: 14px; /* Mac标准字体大小 */
    padding: 8px 16px; /* Mac标准内边距 */
    border: none;
    cursor: pointer;
    -webkit-user-select: none;
    user-select: none;
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
    transition: left 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.btn:hover::before {
    left: 100%;
}

.btn-primary {
    background: #007AFF; /* Mac系统蓝色 */
    color: white;
    box-shadow: 0 1px 3px rgba(0, 122, 255, 0.3);
}

.btn-primary:hover {
    background: #0056CC;
    transform: translateY(-0.5px); /* Mac微妙悬停 */
    box-shadow: 0 2px 8px rgba(0, 122, 255, 0.4);
}

.btn-primary:active {
    transform: translateY(0);
    box-shadow: 0 1px 2px rgba(0, 122, 255, 0.3);
}

.btn-success {
    background: #34C759; /* Mac系统绿色 */
    color: white;
    box-shadow: 0 1px 3px rgba(52, 199, 89, 0.3);
}

.btn-success:hover {
    background: #30B350;
    transform: translateY(-0.5px);
    box-shadow: 0 2px 8px rgba(52, 199, 89, 0.4);
}

.btn-danger {
    background: #FF3B30; /* Mac系统红色 */
    color: white;
    box-shadow: 0 1px 3px rgba(255, 59, 48, 0.3);
}

.btn-danger:hover {
    background: #D70015;
    transform: translateY(-0.5px);
    box-shadow: 0 2px 8px rgba(255, 59, 48, 0.4);
}

/* Mac风格的outline按钮 */
.btn-outline-primary {
    background: rgba(0, 122, 255, 0.1);
    border: 1px solid rgba(0, 122, 255, 0.3);
    color: #007AFF;
}

.btn-outline-primary:hover {
    background: rgba(0, 122, 255, 0.15);
    border-color: #007AFF;
    color: #007AFF;
    transform: translateY(-0.5px);
}

.btn-outline-secondary {
    background: rgba(142, 142, 147, 0.1);
    border: 1px solid rgba(142, 142, 147, 0.3);
    color: #8E8E93;
}

.btn-outline-secondary:hover {
    background: rgba(142, 142, 147, 0.15);
    border-color: #8E8E93;
    color: #8E8E93;
    transform: translateY(-0.5px);
}

/* 进度条样式 */
.progress {
    height: 12px;
    border-radius: 10px;
    background: #e9ecef;
    overflow: hidden;
    box-shadow: inset 0 2px 4px rgba(0,0,0,0.1);
}

.progress-bar {
    background: linear-gradient(135deg, #0d6efd 0%, #0056b3 100%);
    border-radius: 10px;
    transition: width 0.6s ease;
    position: relative;
    overflow: hidden;
}

.progress-bar::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    background-image: linear-gradient(
        -45deg,
        rgba(255, 255, 255, .2) 25%,
        transparent 25%,
        transparent 50%,
        rgba(255, 255, 255, .2) 50%,
        rgba(255, 255, 255, .2) 75%,
        transparent 75%,
        transparent
    );
    background-size: 1rem 1rem;
    animation: progress-bar-stripes 1s linear infinite;
}

@keyframes progress-bar-stripes {
    0% {
        background-position: 1rem 0;
    }
    100% {
        background-position: 0 0;
    }
}

/* 日志输出样式 - Mac Terminal风格 */
.log-output {
    background: #1D1D1F; /* Mac Terminal背景色 */
    color: #F5F5F7; /* Mac Terminal文字色 */
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 12px; /* Mac圆角 */
    padding: 20px;
    font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', 'Source Code Pro', 'Consolas', monospace; /* Mac等宽字体 */
    font-size: 13px;
    max-height: 400px;
    overflow-y: auto;
    white-space: pre-wrap;
    box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.3), 0 1px 3px rgba(0, 0, 0, 0.1);
    line-height: 1.5; /* Mac标准行高 */
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
}

.log-output::-webkit-scrollbar {
    width: 6px; /* Mac细滚动条 */
}

.log-output::-webkit-scrollbar-track {
    background: transparent;
}

.log-output::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.2);
    border-radius: 3px;
    border: 1px solid transparent;
    background-clip: content-box;
}

.log-output::-webkit-scrollbar-thumb:hover {
    background: rgba(255, 255, 255, 0.3);
    background-clip: content-box;
}

/* 连接状态指示器 */
.connection-status {
    position: fixed;
    top: 15px;
    right: 15px;
    z-index: 1000;
    animation: fadeIn 0.5s ease;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(-10px); }
    to { opacity: 1; transform: translateY(0); }
}

/* 状态徽章 */
.status-badge {
    font-size: 0.75em;
    padding: 6px 10px;
    border-radius: 20px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* 结果摘要样式 */
.results-summary {
    background: white;
    border-radius: 10px;
    padding: 20px;
    margin: 15px 0;
    box-shadow: 0 2px 10px rgba(0,0,0,0.05);
}

.result-item {
    text-align: center;
    padding: 15px;
    border-radius: 8px;
    transition: all 0.3s ease;
}

.result-item:hover {
    transform: scale(1.05);
}

.result-item.success {
    background: linear-gradient(135deg, #d1edff 0%, #e7f3ff 100%);
    color: #0c63e4;
}

.result-item.danger {
    background: linear-gradient(135deg, #ffe6e6 0%, #fff0f0 100%);
    color: #dc3545;
}

.result-item.warning {
    background: linear-gradient(135deg, #fff3cd 0%, #fefefe 100%);
    color: #856404;
}

/* 加载动画 */
.loading-spinner {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid #f3f3f3;
    border-top: 3px solid #0d6efd;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Mac通知样式 */
.mac-notification {
    position: fixed;
    top: 60px;
    right: 20px;
    z-index: 9999;
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border: 1px solid rgba(0, 0, 0, 0.1);
    border-radius: 12px;
    padding: 12px 16px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
    transform: translateX(100%);
    opacity: 0;
    transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    max-width: 300px;
    min-width: 200px;
}

.mac-notification.show {
    transform: translateX(0);
    opacity: 1;
}

.mac-notification-content {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
    font-weight: 500;
    color: #1D1D1F;
}

.mac-notification-success {
    border-left: 4px solid #34C759;
}

.mac-notification-error {
    border-left: 4px solid #FF3B30;
}

.mac-notification-warning {
    border-left: 4px solid #FF9500;
}

.mac-notification-info {
    border-left: 4px solid #007AFF;
}

.mac-notification-success .bi {
    color: #34C759;
}

.mac-notification-error .bi {
    color: #FF3B30;
}

.mac-notification-warning .bi {
    color: #FF9500;
}

.mac-notification-info .bi {
    color: #007AFF;
}

/* Mac平台特定样式 */
.mac-platform {
    font-feature-settings: "kern" 1, "liga" 1, "calt" 1;
    text-rendering: optimizeLegibility;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

.mac-platform .btn {
    font-feature-settings: "kern" 1;
}

.mac-platform .card {
    will-change: transform;
}

/* Mac风格的选择框 */
.mac-checkbox {
    appearance: none;
    -webkit-appearance: none;
    width: 18px;
    height: 18px;
    border: 1.5px solid #D1D1D6;
    border-radius: 4px;
    background: white;
    position: relative;
    cursor: pointer;
    transition: all 0.2s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.mac-checkbox:checked {
    background: #007AFF;
    border-color: #007AFF;
}

.mac-checkbox:checked::after {
    content: '';
    position: absolute;
    top: 2px;
    left: 5px;
    width: 6px;
    height: 10px;
    border: solid white;
    border-width: 0 2px 2px 0;
    transform: rotate(45deg);
}

.mac-checkbox:hover {
    border-color: #007AFF;
}

/* 响应式设计 - Mac优化 */
@media (max-width: 768px) {
    .execution-panel {
        position: static;
        margin-top: 20px;
    }

    .test-method {
        margin-left: 10px;
    }

    .file-header {
        padding: 10px 15px;
    }

    .card-body {
        padding: 15px;
    }

    .mac-notification {
        right: 10px;
        left: 10px;
        max-width: none;
        transform: translateY(-100%);
    }

    .mac-notification.show {
        transform: translateY(0);
    }
}

/* 动画效果 */
.fade-in {
    animation: fadeIn 0.5s ease;
}

.slide-up {
    animation: slideUp 0.5s ease;
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 工具提示样式 */
.tooltip {
    font-size: 12px;
}

.tooltip-inner {
    background: #333;
    border-radius: 6px;
    padding: 8px 12px;
}
