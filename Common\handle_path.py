"""
跨平台路径处理模块

提供项目中所有路径的统一管理，确保跨平台兼容性。
所有路径都使用os.path.join()进行构建，避免硬编码路径分隔符。

作者: BlackLake测试框架
版本: 1.1.0 (跨平台增强版)
"""

import os
from pathlib import Path
from typing import Optional

# 获取项目根目录（跨平台兼容）
base_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))

# 核心目录路径（使用os.path.join确保跨平台兼容）
cases_dir = os.path.join(base_dir, "TestCases")           # 测试用例路径
datas_dir = os.path.join(base_dir, "TestDatas")           # 测试数据路径
reports_dir = os.path.join(base_dir, "Outputs", "reports") # 测试报告路径
logs_dir = os.path.join(base_dir, "Outputs", "logs")       # 日志路径
conf_dir = os.path.join(base_dir, "Conf")                 # 配置文件路径
screenshot_dir = os.path.join(base_dir, "Outputs", "screenshots") # 页面截图路径

# 新增路径（跨平台增强）
temp_dir = os.path.join(base_dir, "temp")                 # 临时文件路径
downloads_dir = os.path.join(base_dir, "Outputs", "downloads") # 下载文件路径
drivers_dir = os.path.join(base_dir, "drivers")           # 驱动程序路径
sessions_dir = os.path.join(base_dir, "Outputs", "sessions") # 会话文件路径


def ensure_dir_exists(directory_path: str) -> str:
    """
    确保目录存在，如果不存在则创建

    Args:
        directory_path (str): 目录路径

    Returns:
        str: 目录路径
    """
    Path(directory_path).mkdir(parents=True, exist_ok=True)
    return directory_path


def get_platform_config_dir() -> str:
    """
    获取平台特定的配置目录

    Returns:
        str: 平台配置目录路径
    """
    from Common.platform_utils import get_platform

    platform = get_platform().value
    platform_conf_dir = os.path.join(conf_dir, platform)
    return ensure_dir_exists(platform_conf_dir)


def get_platform_config_file(filename: Optional[str] = None) -> str:
    """
    获取平台特定的配置文件路径

    Args:
        filename (Optional[str]): 配置文件名，如果为None则使用默认名称

    Returns:
        str: 配置文件路径
    """
    from Common.platform_utils import get_platform

    if filename is None:
        platform = get_platform().value
        filename = f"config_{platform}.ini"

    return os.path.join(get_platform_config_dir(), filename)


def get_log_file_path(log_name: Optional[str] = None) -> str:
    """
    获取日志文件路径

    Args:
        log_name (Optional[str]): 日志文件名，如果为None则使用平台默认名称

    Returns:
        str: 日志文件路径
    """
    ensure_dir_exists(logs_dir)

    if log_name is None:
        from Common.platform_utils import get_platform
        platform = get_platform().value
        log_name = f"blacklake_{platform}.log"

    return os.path.join(logs_dir, log_name)


def get_screenshot_path(screenshot_name: str) -> str:
    """
    获取截图文件路径

    Args:
        screenshot_name (str): 截图文件名

    Returns:
        str: 截图文件路径
    """
    ensure_dir_exists(screenshot_dir)
    return os.path.join(screenshot_dir, screenshot_name)


def get_report_path(report_name: str) -> str:
    """
    获取报告文件路径

    Args:
        report_name (str): 报告文件名

    Returns:
        str: 报告文件路径
    """
    ensure_dir_exists(reports_dir)
    return os.path.join(reports_dir, report_name)


def get_temp_file_path(temp_name: str) -> str:
    """
    获取临时文件路径

    Args:
        temp_name (str): 临时文件名

    Returns:
        str: 临时文件路径
    """
    ensure_dir_exists(temp_dir)
    return os.path.join(temp_dir, temp_name)


# 初始化：确保所有必要目录存在
def initialize_directories():
    """初始化所有必要的目录"""
    directories = [
        cases_dir, datas_dir, reports_dir, logs_dir,
        conf_dir, screenshot_dir, temp_dir, downloads_dir,
        drivers_dir, sessions_dir
    ]

    for directory in directories:
        ensure_dir_exists(directory)


# 自动初始化目录
initialize_directories()

