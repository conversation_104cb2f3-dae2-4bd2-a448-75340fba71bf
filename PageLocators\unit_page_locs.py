from selenium.webdriver.common.by import By

# 创建单位
class UnitPageLocs:
    # 单位菜单
    unit_loc = (By.XPATH, "//a[contains(text(), '单位')]")

    # 创建单位
    create_unit_loc = (By.XPATH, "//span[contains(text(), '创建单位')]")

    # 单位名称输入框
    unit_name_loc = (By.XPATH,"//*[@id='name']")

    # 备注输入框
    unit_remark_loc = (By.XPATH, "//*[@id='remark']")

    # 创建单位确定按钮
    # unit_submit_loc = (By.CSS_SELECTOR, "button.ant-btn-primary span")
    unit_submit_loc = (By.XPATH, "//span[contains(text(), '确 定')]")

    # 创建单位取消按钮
    unit_cancel_loc = (By.XPATH, '/html/body/div[9]/div/div[2]/div/div[2]/div[3]/button[1]')

    # 创建单位成功提示
    create_tip_loc = (By.XPATH, "//span[contains(text(), '创建成功！')]")

    # 定位单位名称搜索框
    search_name_loc = (By.XPATH, "//*[@id='likeName']")

    # 点击搜索框
    click_search_loc = (By.XPATH, "//span[contains(text(), '查询')]")

    # 获取查询后的编号
    search_result_loc = (By.XPATH, '//div[@class="kodo-art-table-selection-column-text"]')

