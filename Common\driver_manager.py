"""
跨平台WebDriver管理器

该模块提供跨平台的WebDriver管理功能，自动检测操作系统并配置相应的驱动程序。
支持Chrome、Firefox、Edge等主流浏览器，并提供统一的接口。

作者: BlackLake测试框架
版本: 1.0.0
"""

import os
import sys
import platform
from typing import Optional, Dict, Any
from selenium import webdriver
from selenium.webdriver.chrome.service import Service as ChromeService
from selenium.webdriver.chrome.options import Options as ChromeOptions
from selenium.webdriver.firefox.service import Service as FirefoxService
from selenium.webdriver.firefox.options import Options as FirefoxOptions
from selenium.webdriver.edge.service import Service as EdgeService
from selenium.webdriver.edge.options import Options as EdgeOptions
from webdriver_manager.chrome import ChromeDriverManager
from webdriver_manager.firefox import GeckoDriverManager
from webdriver_manager.microsoft import EdgeChromiumDriverManager

from Common.my_logger import logger


class PlatformInfo:
    """平台信息检测类"""
    
    @staticmethod
    def get_platform() -> str:
        """
        获取当前运行平台
        
        Returns:
            str: 平台标识 ('windows', 'mac', 'linux')
        """
        system = platform.system().lower()
        if system == 'darwin':
            return 'mac'
        elif system == 'windows':
            return 'windows'
        elif system == 'linux':
            return 'linux'
        else:
            logger.warning(f"未知平台: {system}，默认使用linux配置")
            return 'linux'
    
    @staticmethod
    def get_platform_info() -> Dict[str, str]:
        """
        获取详细的平台信息
        
        Returns:
            Dict[str, str]: 包含平台详细信息的字典
        """
        return {
            'system': platform.system(),
            'platform': PlatformInfo.get_platform(),
            'machine': platform.machine(),
            'processor': platform.processor(),
            'python_version': platform.python_version(),
            'sys_platform': sys.platform
        }


class CrossPlatformDriverManager:
    """跨平台WebDriver管理器"""
    
    def __init__(self):
        self.platform = PlatformInfo.get_platform()
        self.platform_info = PlatformInfo.get_platform_info()
        logger.info(f"初始化跨平台驱动管理器 - 平台: {self.platform}")
        logger.debug(f"平台详细信息: {self.platform_info}")
    
    def get_chrome_options(self, headless: bool = False, **kwargs) -> ChromeOptions:
        """
        获取Chrome浏览器选项（跨平台兼容）
        
        Args:
            headless (bool): 是否启用无头模式
            **kwargs: 其他自定义选项
            
        Returns:
            ChromeOptions: Chrome浏览器选项对象
        """
        options = ChromeOptions()
        
        # 基础选项
        basic_args = [
            '--no-sandbox',
            '--disable-dev-shm-usage',
            '--disable-gpu',
            '--disable-extensions',
            '--disable-logging',
            '--disable-background-timer-throttling',
            '--disable-backgrounding-occluded-windows',
            '--disable-renderer-backgrounding'
        ]
        
        for arg in basic_args:
            options.add_argument(arg)
        
        # 平台特定选项
        if self.platform == 'mac':
            mac_args = [
                '--disable-web-security',
                '--allow-running-insecure-content',
                '--disable-features=VizDisplayCompositor'
            ]
            for arg in mac_args:
                options.add_argument(arg)
                
        elif self.platform == 'windows':
            windows_args = [
                '--disable-features=VizDisplayCompositor',
                '--disable-background-networking'
            ]
            for arg in windows_args:
                options.add_argument(arg)
                
        elif self.platform == 'linux':
            linux_args = [
                '--disable-background-networking',
                '--disable-default-apps',
                '--disable-sync'
            ]
            for arg in linux_args:
                options.add_argument(arg)
        
        # 无头模式
        if headless:
            options.add_argument('--headless')
            options.add_argument('--disable-gpu')
        
        # 自定义选项
        for key, value in kwargs.items():
            if key.startswith('add_argument'):
                options.add_argument(value)
            elif key == 'window_size':
                options.add_argument(f'--window-size={value}')
            elif key == 'user_agent':
                options.add_argument(f'--user-agent={value}')
        
        logger.debug(f"Chrome选项配置完成 - 平台: {self.platform}, 无头模式: {headless}")
        return options
    
    def get_chrome_driver(self, headless: bool = False, **kwargs) -> webdriver.Chrome:
        """
        获取Chrome WebDriver实例
        
        Args:
            headless (bool): 是否启用无头模式
            **kwargs: 其他配置选项
            
        Returns:
            webdriver.Chrome: Chrome WebDriver实例
        """
        try:
            logger.info(f"正在初始化Chrome WebDriver - 平台: {self.platform}")
            
            # 自动下载并获取驱动程序路径
            driver_path = ChromeDriverManager().install()
            logger.debug(f"Chrome驱动程序路径: {driver_path}")
            
            # 创建服务对象
            service = ChromeService(driver_path)
            
            # 获取选项配置
            options = self.get_chrome_options(headless=headless, **kwargs)
            
            # 创建WebDriver实例
            driver = webdriver.Chrome(service=service, options=options)
            
            logger.info("Chrome WebDriver初始化成功")
            return driver
            
        except Exception as e:
            logger.error(f"Chrome WebDriver初始化失败: {str(e)}")
            raise
    
    def get_firefox_options(self, headless: bool = False, **kwargs) -> FirefoxOptions:
        """
        获取Firefox浏览器选项（跨平台兼容）
        
        Args:
            headless (bool): 是否启用无头模式
            **kwargs: 其他自定义选项
            
        Returns:
            FirefoxOptions: Firefox浏览器选项对象
        """
        options = FirefoxOptions()
        
        # 基础选项
        options.set_preference("dom.webdriver.enabled", False)
        options.set_preference("useAutomationExtension", False)
        
        # 平台特定配置
        if self.platform == 'mac':
            options.set_preference("security.sandbox.content.level", 5)
        
        # 无头模式
        if headless:
            options.add_argument('--headless')
        
        logger.debug(f"Firefox选项配置完成 - 平台: {self.platform}, 无头模式: {headless}")
        return options
    
    def get_firefox_driver(self, headless: bool = False, **kwargs) -> webdriver.Firefox:
        """
        获取Firefox WebDriver实例
        
        Args:
            headless (bool): 是否启用无头模式
            **kwargs: 其他配置选项
            
        Returns:
            webdriver.Firefox: Firefox WebDriver实例
        """
        try:
            logger.info(f"正在初始化Firefox WebDriver - 平台: {self.platform}")
            
            # 自动下载并获取驱动程序路径
            driver_path = GeckoDriverManager().install()
            logger.debug(f"Firefox驱动程序路径: {driver_path}")
            
            # 创建服务对象
            service = FirefoxService(driver_path)
            
            # 获取选项配置
            options = self.get_firefox_options(headless=headless, **kwargs)
            
            # 创建WebDriver实例
            driver = webdriver.Firefox(service=service, options=options)
            
            logger.info("Firefox WebDriver初始化成功")
            return driver
            
        except Exception as e:
            logger.error(f"Firefox WebDriver初始化失败: {str(e)}")
            raise
    
    def get_driver(self, browser: str = 'chrome', headless: bool = False, **kwargs):
        """
        获取指定浏览器的WebDriver实例
        
        Args:
            browser (str): 浏览器类型 ('chrome', 'firefox', 'edge')
            headless (bool): 是否启用无头模式
            **kwargs: 其他配置选项
            
        Returns:
            WebDriver: 对应浏览器的WebDriver实例
        """
        browser = browser.lower()
        
        if browser == 'chrome':
            return self.get_chrome_driver(headless=headless, **kwargs)
        elif browser == 'firefox':
            return self.get_firefox_driver(headless=headless, **kwargs)
        else:
            raise ValueError(f"不支持的浏览器类型: {browser}")


# 全局实例
driver_manager = CrossPlatformDriverManager()


def get_platform_driver(browser: str = 'chrome', headless: bool = False, **kwargs):
    """
    便捷函数：获取跨平台WebDriver实例
    
    Args:
        browser (str): 浏览器类型
        headless (bool): 是否启用无头模式
        **kwargs: 其他配置选项
        
    Returns:
        WebDriver: WebDriver实例
    """
    return driver_manager.get_driver(browser=browser, headless=headless, **kwargs)


if __name__ == '__main__':
    # 测试代码
    print("平台信息:")
    info = PlatformInfo.get_platform_info()
    for key, value in info.items():
        print(f"  {key}: {value}")
    
    print("\n测试Chrome WebDriver初始化...")
    try:
        driver = get_platform_driver('chrome')
        print("Chrome WebDriver初始化成功!")
        driver.quit()
    except Exception as e:
        print(f"Chrome WebDriver初始化失败: {e}")
