# Mac平台专用依赖包配置
# BlackLake测试框架 - Mac兼容版本

# 核心测试框架
pytest==7.4.2
allure-pytest==2.13.2

# Web自动化核心
selenium==4.15.0
webdriver-manager==4.0.1

# Web应用框架
Flask==2.3.3
Flask-SocketIO==5.3.6

# 异步和WebSocket支持
python-socketio==5.8.0
python-engineio==4.7.1
gevent==23.9.1
gevent-websocket==0.10.1

# Mac平台特定优化包
psutil==5.9.5  # 系统信息获取
pathlib2==2.3.7  # 路径处理增强（Python < 3.4兼容）

# 日志和调试
colorlog==6.7.0  # 彩色日志输出
