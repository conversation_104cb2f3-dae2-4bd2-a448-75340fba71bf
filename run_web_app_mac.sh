#!/bin/bash

# BlackLake测试框架 - Mac Web应用启动脚本
# 版本: 1.0.0

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查虚拟环境
check_virtual_env() {
    if [ ! -d "venv" ]; then
        log_error "虚拟环境不存在！请先运行 ./setup_mac.sh"
        exit 1
    fi
    
    log_info "激活虚拟环境..."
    source venv/bin/activate
    
    # 验证Python环境
    python --version
    pip --version
}

# 检查依赖包
check_dependencies() {
    log_info "检查Web应用依赖..."
    
    required_packages=("flask" "flask-socketio" "selenium" "webdriver-manager")
    
    for package in "${required_packages[@]}"; do
        if python -c "import ${package//-/_}" 2>/dev/null; then
            log_success "✓ $package"
        else
            log_error "✗ $package 未安装"
            log_info "正在安装 $package..."
            pip install $package
        fi
    done
}

# 设置环境变量
setup_environment() {
    log_info "设置环境变量..."
    
    export PYTHONPATH="${PYTHONPATH}:$(pwd)"
    export FLASK_ENV=development
    export FLASK_DEBUG=1
    export BLACKLAKE_PLATFORM=mac
    export BLACKLAKE_LOG_LEVEL=INFO
    
    # Mac特定环境变量
    export OBJC_DISABLE_INITIALIZE_FORK_SAFETY=YES
    
    # Web应用配置
    export BLACKLAKE_WEB_HOST=${BLACKLAKE_WEB_HOST:-"127.0.0.1"}
    export BLACKLAKE_WEB_PORT=${BLACKLAKE_WEB_PORT:-"5000"}
    
    log_success "环境变量设置完成"
}

# 检查端口是否被占用
check_port() {
    local port=${1:-5000}
    
    if lsof -Pi :$port -sTCP:LISTEN -t >/dev/null 2>&1; then
        log_warning "端口 $port 已被占用"
        
        # 显示占用端口的进程
        local pid=$(lsof -Pi :$port -sTCP:LISTEN -t)
        local process=$(ps -p $pid -o comm= 2>/dev/null || echo "未知进程")
        log_info "占用进程: $process (PID: $pid)"
        
        # 询问是否终止进程
        read -p "是否终止占用端口的进程? (y/n): " -n 1 -r
        echo
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            kill -9 $pid 2>/dev/null || true
            log_success "进程已终止"
        else
            log_info "请手动终止进程或使用其他端口"
            log_info "使用其他端口: export BLACKLAKE_WEB_PORT=8080"
            exit 1
        fi
    fi
}

# 启动Web应用
start_web_app() {
    local app_file="web_app.py"
    local simple_app_file="simple_web_app.py"
    
    log_info "启动BlackLake Web应用..."
    
    # 检查应用文件
    if [ -f "$app_file" ]; then
        log_info "使用完整版Web应用: $app_file"
        app_to_run="$app_file"
    elif [ -f "$simple_app_file" ]; then
        log_info "使用简化版Web应用: $simple_app_file"
        app_to_run="$simple_app_file"
    else
        log_error "未找到Web应用文件！"
        exit 1
    fi
    
    # 检查端口
    check_port $BLACKLAKE_WEB_PORT
    
    # 显示启动信息
    echo ""
    log_success "=== BlackLake Mac版Web应用启动信息 ==="
    echo "应用文件: $app_to_run"
    echo "访问地址: http://$BLACKLAKE_WEB_HOST:$BLACKLAKE_WEB_PORT"
    echo "Mac专用界面: http://$BLACKLAKE_WEB_HOST:$BLACKLAKE_WEB_PORT/mac"
    echo "经典界面: http://$BLACKLAKE_WEB_HOST:$BLACKLAKE_WEB_PORT/classic"
    echo "平台: Mac (已优化)"
    echo "环境: 开发模式"
    echo ""
    log_info "Mac快捷键: Cmd+R刷新, Cmd+Q退出"
    log_info "按 Ctrl+C 停止应用"
    echo ""
    
    # 启动应用
    python "$app_to_run"
}

# 打开浏览器
open_browser() {
    local base_url="http://$BLACKLAKE_WEB_HOST:$BLACKLAKE_WEB_PORT"
    local mac_url="$base_url/mac"

    log_info "等待Web应用启动..."
    sleep 3

    if command -v open &> /dev/null; then
        log_info "在默认浏览器中打开Mac优化界面: $mac_url"
        open "$mac_url" &

        # 可选：同时打开经典界面
        # sleep 1
        # open "$base_url" &
    else
        log_info "请手动在浏览器中访问:"
        log_info "  Mac优化界面: $mac_url"
        log_info "  经典界面: $base_url"
    fi
}

# 清理函数
cleanup() {
    log_info "清理临时文件..."
    
    # 清理Python缓存
    find . -name "__pycache__" -type d -exec rm -rf {} + 2>/dev/null || true
    find . -name "*.pyc" -delete 2>/dev/null || true
    
    log_success "清理完成"
}

# 显示帮助信息
show_help() {
    echo "BlackLake测试框架 - Mac Web应用启动脚本"
    echo ""
    echo "使用方法:"
    echo "  $0 [选项]"
    echo ""
    echo "选项:"
    echo "  --help      显示此帮助信息"
    echo "  --clean     启动前清理环境"
    echo "  --no-browser 不自动打开浏览器"
    echo "  --port PORT 指定端口号（默认5000）"
    echo "  --host HOST 指定主机地址（默认127.0.0.1）"
    echo ""
    echo "环境变量:"
    echo "  BLACKLAKE_WEB_HOST  Web应用主机地址"
    echo "  BLACKLAKE_WEB_PORT  Web应用端口号"
    echo ""
    echo "示例:"
    echo "  $0                    # 启动Web应用并打开浏览器"
    echo "  $0 --no-browser      # 启动Web应用但不打开浏览器"
    echo "  $0 --port 8080       # 使用8080端口启动"
    echo "  $0 --host 0.0.0.0    # 允许外部访问"
    echo ""
}

# 信号处理
trap 'log_info "正在停止Web应用..."; cleanup; exit 0' INT TERM

# 主函数
main() {
    echo ""
    log_info "=== BlackLake测试框架 Mac版Web应用启动器 ==="
    echo ""
    
    local open_browser_flag=true
    
    # 处理命令行参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            --help|-h)
                show_help
                exit 0
                ;;
            --clean)
                cleanup
                ;;
            --no-browser)
                open_browser_flag=false
                ;;
            --port)
                export BLACKLAKE_WEB_PORT="$2"
                shift
                ;;
            --host)
                export BLACKLAKE_WEB_HOST="$2"
                shift
                ;;
            *)
                log_error "未知参数: $1"
                show_help
                exit 1
                ;;
        esac
        shift
    done
    
    # 检查环境
    check_virtual_env
    check_dependencies
    setup_environment
    
    # 在后台打开浏览器
    if [ "$open_browser_flag" = true ]; then
        open_browser &
    fi
    
    # 启动Web应用（前台运行）
    start_web_app
}

# 运行主函数
main "$@"
