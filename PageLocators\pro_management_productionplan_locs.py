
from selenium.webdriver.common.by import By



# 生成管理---> 生产计划
class ProductionPlanLocs:
    # -----------------------------------------创建报工-----------------------------------------
    # 生产计划菜单
    production_plan_loc = (By.XPATH, "//a[@href='/productionManagement/customFormList/102']")
    # production_plan_loc = (By.XPATH, "//font[text()='生产计划']")
    # 创建生产计划
    # create_production_plan_loc  = (By.XPATH,"//button[contains(., '制定生产计划')]")
    create_production_plan_loc = (By.CSS_SELECTOR, "div.ant-space-item > button")
    # 单据编号
    orders_codes_loc = (By.XPATH, '//*[@id="serialCode"]')
    # 滑动页面向下
    scroll_down_loc = (By.XPATH, '//*[@id="main-layout"]/div/div/div/div[1]/form/div[2]/div[10]/div/div[2]/div/div/input')
    # 整个页面
    all_production_plan_loc = (By.XPATH, '//*[@id="main-layout"]/div/div/div/div[1]')
    # 添加产品
    add_production_plan_loc = (By.XPATH, "//button[contains(., '添加')]")
    # 创建新的产品
    # create_new_product = (By.XPATH, "//span[.//font[contains(.,'新建产品')]]")
    create_new_product = (By.XPATH, "//button[contains(@class, 'kodo-new-btn') and contains(., '新建产品')]")
    # 新的产品名称
    create_new_product_name = (By.XPATH, '//*[@id="name"]')
    # 展开新的产品
    create_new_product_unit = (By.CSS_SELECTOR, "div.kodo-new-form-item > div.kodo-new-row > div.kodo-new-form-item-control > div > div > div.kodo-new-select")
    # 选择新的产品第一个
    select_new_product_unit = (By.XPATH,"//div[contains(@class,'rc-virtual-list-holder-inner')]/div[contains(@class,'kodo-new-select-item-option')][1]")
    # 点击添加新品确定
    new_product_sure_loc = (By.XPATH, "//button[contains(@class,'kodo-new-btn-primary') and .//span[text()='确 定']]")
    # 全选
    all_select_loc = (By.XPATH, "(//div[@data-cell-id='kodo-art-table-header-cell-selection'])[2]")
    # 点击确定
    click_sure_loc = (By.XPATH, "//span[contains(text(), '确定')]")
    # 点击创造
    click_create_loc = (By.XPATH, "//span[text()='创建']")
    # 创建成功提示
    sure_success_loc = (By.XPATH, "//span[contains(text(), '创建成功')]")


    # -----------------------------------------编辑报工-----------------------------------------
    # 点击编辑
    update_production_plan = (By.XPATH, "(//div[contains(@class, 'btn__FtXkI') and contains(., '编辑')])[1]")

    # 更新工单编号
    update_order_code = (By.CSS_SELECTOR, "input[placeholder='请输入，忽略将自动生成']")
    # 点击确认
    click_to_confirm_update = (By.CSS_SELECTOR, "button[type='button'].kodo-new-btn-primary")
    # 成功断言
    successfully_update_text = (By.XPATH, "//span[contains(.,'编辑成功')]")

    # -----------------------------------------删除报工-----------------------------------------
    # 点击操作
    click_operation_loc = (By.CSS_SELECTOR, "div.kodo-new-dropdown-trigger.btn__FtXkI")

    # 点击删除
    click_to_delete = (By.XPATH, "//li[contains(@class, 'list-item')]//a[contains(., '删除')]")
    # 点击删除确认
    click_to_confirm_deletion = (By.CSS_SELECTOR, "button[type='button'].kodo-new-btn-dangerous")
    # 删除成功断言
    successfully_deleted_text = (By.XPATH, "//div[contains(@class,'kodo-new-message-success')]//*[contains(.,'删除成功！')]")










