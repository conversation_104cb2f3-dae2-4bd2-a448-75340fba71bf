#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
BlackLake测试框架 - Mac优化版Web应用
基于Flask框架，提供跨平台的测试用例可视化管理和执行控制
专为Mac平台优化的用户界面和交互体验
"""

import os
import sys
import json
import subprocess
import threading
import time
import platform
from datetime import datetime
from pathlib import Path
from flask import Flask, render_template, request, jsonify, send_file, user_agent
from flask_socketio import SocketIO, emit
import pytest
import glob

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

from Common.handle_path import cases_dir, reports_dir
from Common.platform_utils import get_platform, is_mac, get_platform_info

app = Flask(__name__)
app.config['SECRET_KEY'] = 'test_execution_control_secret_key'
socketio = SocketIO(app, cors_allowed_origins="*", async_mode='threading')

# 全局变量存储测试执行状态
test_execution_status = {
    'running': False,
    'current_test': None,
    'progress': 0,
    'total_tests': 0,
    'results': [],
    'start_time': None,
    'end_time': None
}

class TestCaseDiscovery:
    """测试用例发现和管理类"""

    def __init__(self, test_dir=None):
        self.test_dir = test_dir or cases_dir

    def discover_test_files(self):
        """发现所有测试文件"""
        test_files = []

        # 支持多种测试文件命名模式
        patterns = [
            os.path.join(self.test_dir, "test_*.py"),
            os.path.join(self.test_dir, "*test*.py"),
            os.path.join(self.test_dir, "atest_*.py")  # 支持项目中的atest_前缀
        ]

        for pattern in patterns:
            for file_path in glob.glob(pattern):
                if os.path.basename(file_path) not in ['conftest.py', '__init__.py']:
                    test_files.append(file_path)

        return list(set(test_files))  # 去重

    def parse_test_cases(self, file_path):
        """解析单个文件中的测试用例"""
        test_cases = []
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()

            # 使用更精确的解析方法
            lines = content.split('\n')
            current_class = None
            in_class = False
            class_indent = 0

            for line_num, line in enumerate(lines, 1):
                stripped_line = line.strip()

                # 计算缩进级别
                indent_level = len(line) - len(line.lstrip())

                # 查找测试类
                if stripped_line.startswith('class Test') and ':' in stripped_line:
                    current_class = stripped_line.split('class ')[1].split('(')[0].split(':')[0].strip()
                    in_class = True
                    class_indent = indent_level
                    continue

                # 检查是否还在类内部
                if in_class and indent_level <= class_indent and stripped_line and not stripped_line.startswith('#'):
                    if not stripped_line.startswith('def') and not stripped_line.startswith('@'):
                        in_class = False
                        current_class = None

                # 查找测试方法
                if stripped_line.startswith('def test_') and '(' in stripped_line:
                    method_name = stripped_line.split('def ')[1].split('(')[0].strip()

                    # 提取方法的文档字符串作为描述
                    description = self._extract_docstring(lines, line_num)

                    test_case = {
                        'file': os.path.basename(file_path),
                        'file_path': file_path,
                        'class': current_class,
                        'method': method_name,
                        'line': line_num,
                        'description': description,
                        'full_name': f"{current_class}::{method_name}" if current_class else method_name,
                        'test_id': f"{file_path}::{current_class}::{method_name}" if current_class else f"{file_path}::{method_name}"
                    }
                    test_cases.append(test_case)

        except Exception as e:
            print(f"解析文件 {file_path} 时出错: {e}")

        return test_cases

    def _extract_docstring(self, lines, method_line_num):
        """提取方法的文档字符串"""
        try:
            # 查找方法后的文档字符串
            for i in range(method_line_num, min(method_line_num + 5, len(lines))):
                line = lines[i].strip()
                if line.startswith('"""') or line.startswith("'''"):
                    # 单行文档字符串
                    if line.count('"""') == 2 or line.count("'''") == 2:
                        return line.strip('"""').strip("'''").strip()
                    # 多行文档字符串开始
                    else:
                        docstring_lines = [line.strip('"""').strip("'''")]
                        for j in range(i + 1, min(i + 10, len(lines))):
                            next_line = lines[j].strip()
                            if next_line.endswith('"""') or next_line.endswith("'''"):
                                docstring_lines.append(next_line.strip('"""').strip("'''"))
                                return ' '.join(docstring_lines).strip()
                            else:
                                docstring_lines.append(next_line)
                elif line.startswith('#'):
                    # 注释作为描述
                    return line.lstrip('#').strip()
        except:
            pass
        return ""

    def get_all_test_cases(self):
        """获取所有测试用例"""
        all_cases = []
        test_files = self.discover_test_files()

        for file_path in test_files:
            cases = self.parse_test_cases(file_path)
            all_cases.extend(cases)

        return all_cases

    def get_test_files_summary(self):
        """获取测试文件摘要信息"""
        test_files = self.discover_test_files()
        summary = []

        for file_path in test_files:
            cases = self.parse_test_cases(file_path)
            summary.append({
                'file': os.path.basename(file_path),
                'file_path': file_path,
                'test_count': len(cases),
                'classes': list(set([case['class'] for case in cases if case['class']])),
                'last_modified': os.path.getmtime(file_path)
            })

        return summary

class TestExecutor:
    """测试执行器"""

    def __init__(self):
        self.discovery = TestCaseDiscovery()
        self.current_process = None

    def execute_single_test(self, file_path, test_name=None):
        """执行单个测试用例"""
        global test_execution_status

        test_execution_status['running'] = True
        test_execution_status['start_time'] = datetime.now()
        test_execution_status['current_test'] = test_name or os.path.basename(file_path)
        test_execution_status['progress'] = 0
        test_execution_status['total_tests'] = 1
        test_execution_status['results'] = []

        try:
            socketio.emit('test_started', {
                'test': test_name or file_path,
                'message': f'开始执行: {test_name or os.path.basename(file_path)}'
            })

            # 构建pytest命令
            if test_name and '::' in test_name:
                # 执行特定的测试方法
                cmd = [
                    sys.executable, '-m', 'pytest',
                    f"{file_path}::{test_name}",
                    '-v', '--tb=short', '--no-header',
                    f'--alluredir={reports_dir}/allure-results'
                ]
            else:
                # 执行整个文件
                cmd = [
                    sys.executable, '-m', 'pytest',
                    file_path,
                    '-v', '--tb=short', '--no-header',
                    f'--alluredir={reports_dir}/allure-results'
                ]

            socketio.emit('test_progress', {
                'progress': 10,
                'message': '正在执行测试...'
            })

            # 执行测试
            self.current_process = subprocess.Popen(
                cmd,
                cwd=project_root,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                encoding='utf-8'
            )

            # 实时读取输出
            stdout, stderr = self.current_process.communicate()

            test_execution_status['progress'] = 100
            test_execution_status['end_time'] = datetime.now()

            # 解析结果
            success = self.current_process.returncode == 0
            test_execution_status['results'] = [{
                'test': test_name or os.path.basename(file_path),
                'status': 'PASSED' if success else 'FAILED',
                'output': stdout,
                'error': stderr,
                'duration': (test_execution_status['end_time'] - test_execution_status['start_time']).total_seconds()
            }]

            # 通知前端
            socketio.emit('test_completed', {
                'success': success,
                'results': test_execution_status['results'],
                'output': stdout,
                'error': stderr
            })

        except Exception as e:
            test_execution_status['results'] = [{
                'test': test_name or file_path,
                'status': 'ERROR',
                'error': str(e)
            }]
            socketio.emit('test_error', {'error': str(e)})

        finally:
            test_execution_status['running'] = False
            test_execution_status['current_test'] = None
            self.current_process = None

    def execute_multiple_tests(self, test_list):
        """执行多个测试用例"""
        global test_execution_status

        test_execution_status['running'] = True
        test_execution_status['start_time'] = datetime.now()
        test_execution_status['progress'] = 0
        test_execution_status['total_tests'] = len(test_list)
        test_execution_status['results'] = []

        try:
            socketio.emit('test_started', {
                'message': f'开始批量执行 {len(test_list)} 个测试'
            })

            # 构建pytest命令，执行指定的测试
            cmd = [
                sys.executable, '-m', 'pytest',
                '-v', '--tb=short', '--no-header',
                f'--alluredir={reports_dir}/allure-results'
            ]

            # 添加测试文件或测试用例
            for test_item in test_list:
                if '::' in test_item:
                    cmd.append(test_item)
                else:
                    cmd.append(test_item)

            socketio.emit('test_progress', {
                'progress': 10,
                'message': '正在执行批量测试...'
            })

            # 执行测试
            self.current_process = subprocess.Popen(
                cmd,
                cwd=project_root,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                encoding='utf-8'
            )

            stdout, stderr = self.current_process.communicate()

            test_execution_status['progress'] = 100
            test_execution_status['end_time'] = datetime.now()

            # 解析结果
            success = self.current_process.returncode == 0
            test_execution_status['results'] = self._parse_pytest_output(stdout, stderr)

            # 通知前端
            socketio.emit('test_completed', {
                'success': success,
                'results': test_execution_status['results'],
                'output': stdout,
                'error': stderr
            })

        except Exception as e:
            socketio.emit('test_error', {'error': str(e)})

        finally:
            test_execution_status['running'] = False
            test_execution_status['current_test'] = None
            self.current_process = None

    def execute_all_tests(self):
        """执行所有测试用例"""
        global test_execution_status

        test_execution_status['running'] = True
        test_execution_status['start_time'] = datetime.now()
        test_execution_status['progress'] = 0
        test_execution_status['results'] = []

        try:
            socketio.emit('test_started', {
                'message': '开始执行所有测试用例'
            })

            # 构建pytest命令，执行TestCases目录下的所有测试
            cmd = [
                sys.executable, '-m', 'pytest',
                cases_dir,
                '-v', '--tb=short', '--no-header',
                f'--alluredir={reports_dir}/allure-results'
            ]

            socketio.emit('test_progress', {
                'progress': 10,
                'message': '正在执行所有测试...'
            })

            # 执行测试
            self.current_process = subprocess.Popen(
                cmd,
                cwd=project_root,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                encoding='utf-8'
            )

            stdout, stderr = self.current_process.communicate()

            test_execution_status['progress'] = 100
            test_execution_status['end_time'] = datetime.now()

            # 解析结果
            success = self.current_process.returncode == 0
            test_execution_status['results'] = self._parse_pytest_output(stdout, stderr)

            # 通知前端
            socketio.emit('test_completed', {
                'success': success,
                'results': test_execution_status['results'],
                'output': stdout,
                'error': stderr
            })

        except Exception as e:
            socketio.emit('test_error', {'error': str(e)})

        finally:
            test_execution_status['running'] = False
            test_execution_status['current_test'] = None
            self.current_process = None

    def _parse_pytest_output(self, stdout, stderr):
        """解析pytest输出，提取测试结果"""
        results = []

        try:
            lines = stdout.split('\n')
            for line in lines:
                line = line.strip()
                if '::' in line and ('PASSED' in line or 'FAILED' in line or 'ERROR' in line):
                    parts = line.split()
                    if len(parts) >= 2:
                        test_name = parts[0]
                        status = parts[-1]
                        results.append({
                            'test': test_name,
                            'status': status,
                            'output': line
                        })
        except Exception as e:
            print(f"解析pytest输出时出错: {e}")

        return results

    def stop_execution(self):
        """停止当前执行的测试"""
        if self.current_process:
            try:
                self.current_process.terminate()
                self.current_process.wait(timeout=5)
            except subprocess.TimeoutExpired:
                self.current_process.kill()
            finally:
                self.current_process = None

        test_execution_status['running'] = False
        test_execution_status['current_test'] = None

# 创建测试执行器实例
test_executor = TestExecutor()

def detect_client_platform(request):
    """检测客户端平台"""
    user_agent_string = request.headers.get('User-Agent', '').lower()

    if 'mac' in user_agent_string or 'darwin' in user_agent_string:
        return 'mac'
    elif 'windows' in user_agent_string or 'win' in user_agent_string:
        return 'windows'
    elif 'linux' in user_agent_string:
        return 'linux'
    else:
        return 'unknown'

@app.route('/')
def index():
    """主页 - 智能平台检测"""
    client_platform = detect_client_platform(request)
    server_platform = get_platform().value

    # 如果客户端是Mac或服务器是Mac，使用Mac优化版界面
    if client_platform == 'mac' or server_platform == 'mac':
        return render_template('index_mac.html',
                             client_platform=client_platform,
                             server_platform=server_platform)
    else:
        return render_template('index.html',
                             client_platform=client_platform,
                             server_platform=server_platform)

@app.route('/mac')
def mac_interface():
    """Mac专用界面"""
    return render_template('index_mac.html',
                         client_platform='mac',
                         server_platform=get_platform().value)

@app.route('/classic')
def classic_interface():
    """经典界面"""
    return render_template('index.html',
                         client_platform=detect_client_platform(request),
                         server_platform=get_platform().value)

@app.route('/api/platform-info')
def get_platform_info_api():
    """获取平台信息API"""
    try:
        client_platform = detect_client_platform(request)
        server_platform_info = get_platform_info()

        return jsonify({
            'success': True,
            'data': {
                'client_platform': client_platform,
                'server_platform': server_platform_info,
                'is_mac_optimized': client_platform == 'mac' or server_platform_info['platform_type'] == 'mac',
                'recommended_interface': 'mac' if (client_platform == 'mac' or server_platform_info['platform_type'] == 'mac') else 'classic'
            }
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/test-cases')
def get_test_cases():
    """获取所有测试用例"""
    try:
        discovery = TestCaseDiscovery()
        test_cases = discovery.get_all_test_cases()
        return jsonify({
            'success': True,
            'test_cases': test_cases,
            'total': len(test_cases)
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/execute-test', methods=['POST'])
def execute_test():
    """执行测试用例"""
    try:
        data = request.get_json()
        file_path = data.get('file_path')
        test_name = data.get('test_name')
        test_list = data.get('test_list', [])  # 支持批量执行

        if not file_path and not test_list:
            return jsonify({
                'success': False,
                'error': '缺少文件路径或测试列表参数'
            }), 400

        if test_execution_status['running']:
            return jsonify({
                'success': False,
                'error': '已有测试正在执行中'
            }), 409

        # 在后台线程中执行测试
        if test_list:
            # 批量执行
            thread = threading.Thread(
                target=test_executor.execute_multiple_tests,
                args=(test_list,)
            )
        else:
            # 单个执行
            thread = threading.Thread(
                target=test_executor.execute_single_test,
                args=(file_path, test_name)
            )

        thread.daemon = True
        thread.start()

        return jsonify({
            'success': True,
            'message': '测试开始执行'
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/execute-all', methods=['POST'])
def execute_all_tests():
    """执行所有测试用例"""
    try:
        if test_execution_status['running']:
            return jsonify({
                'success': False,
                'error': '已有测试正在执行中'
            }), 409

        # 在后台线程中执行所有测试
        thread = threading.Thread(
            target=test_executor.execute_all_tests
        )
        thread.daemon = True
        thread.start()

        return jsonify({
            'success': True,
            'message': '开始执行所有测试'
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/stop-execution', methods=['POST'])
def stop_execution():
    """停止测试执行"""
    try:
        # 这里可以实现停止逻辑，比如终止子进程
        test_execution_status['running'] = False
        test_execution_status['current_test'] = None

        socketio.emit('execution_stopped', {
            'message': '测试执行已停止'
        })

        return jsonify({
            'success': True,
            'message': '测试执行已停止'
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/test-status')
def get_test_status():
    """获取测试执行状态"""
    return jsonify(test_execution_status)

@app.route('/api/test-files')
def get_test_files():
    """获取测试文件摘要"""
    try:
        discovery = TestCaseDiscovery()
        files_summary = discovery.get_test_files_summary()
        return jsonify({
            'success': True,
            'files': files_summary
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/generate-report')
def generate_allure_report():
    """生成Allure报告"""
    try:
        allure_results_dir = os.path.join(reports_dir, 'allure-results')
        allure_report_dir = os.path.join(reports_dir, 'allure-report')

        if not os.path.exists(allure_results_dir) or not os.listdir(allure_results_dir):
            return jsonify({
                'success': False,
                'error': '没有找到测试结果文件，请先执行测试'
            }), 400

        # 生成Allure报告
        cmd = ['allure', 'generate', allure_results_dir, '-o', allure_report_dir, '--clean']

        try:
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)
            if result.returncode == 0:
                return jsonify({
                    'success': True,
                    'message': '报告生成成功',
                    'report_path': allure_report_dir
                })
            else:
                return jsonify({
                    'success': False,
                    'error': f'报告生成失败: {result.stderr}'
                }), 500
        except FileNotFoundError:
            return jsonify({
                'success': False,
                'error': '未找到allure命令，请确保已安装Allure命令行工具'
            }), 500
        except subprocess.TimeoutExpired:
            return jsonify({
                'success': False,
                'error': '报告生成超时'
            }), 500

    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/view-report')
def view_report():
    """查看Allure报告"""
    try:
        allure_report_dir = os.path.join(reports_dir, 'allure-report')
        index_file = os.path.join(allure_report_dir, 'index.html')

        if not os.path.exists(index_file):
            return jsonify({
                'success': False,
                'error': '报告文件不存在，请先生成报告'
            }), 404

        return send_file(index_file)

    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/reports/<path:filename>')
def serve_report_files(filename):
    """提供报告文件服务"""
    try:
        allure_report_dir = os.path.join(reports_dir, 'allure-report')
        return send_file(os.path.join(allure_report_dir, filename))
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 404

@socketio.on('connect')
def handle_connect():
    """WebSocket连接处理"""
    emit('connected', {'message': '连接成功'})

@socketio.on('disconnect')
def handle_disconnect():
    """WebSocket断开连接处理"""
    print('客户端断开连接')

if __name__ == '__main__':
    # 确保必要的目录存在
    os.makedirs(reports_dir, exist_ok=True)
    os.makedirs(os.path.join(reports_dir, 'allure-results'), exist_ok=True)

    print("启动测试执行控制Web应用...")
    print(f"测试用例目录: {cases_dir}")
    print(f"报告目录: {reports_dir}")
    print("访问地址: http://localhost:5000")

    try:
        socketio.run(app, debug=False, host='0.0.0.0', port=5000, allow_unsafe_werkzeug=True)
    except Exception as e:
        print(f"启动失败: {e}")
        print("尝试使用备用启动方式...")
        app.run(debug=False, host='0.0.0.0', port=5000)
