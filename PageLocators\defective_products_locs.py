# 创建不良品项
from selenium.webdriver.common.by import By

class DefectiveProductsPageLocs:

    #点击不良品项菜单
    product_defects_loc = (By.XPATH, "//a[contains(text(), '不良品项')]")

    # 创建不良品项
    create_product_defects_loc = (By.XPATH,"//span[contains(text(), '创建不良品项')]")

    # 输入不良品项名称
    product_defects_name_loc = (By.XPATH,'//*[@id="name"]')

    # 创建单位确定按钮
    unit_submit_loc = (By.XPATH, "//span[contains(text(), '确 定')]")

    # 创建单位成功提示
    create_tip_loc = (By.XPATH, "//span[contains(text(), '创建成功！')]")

    # -----------------------------------------编辑不良品项-----------------------------------------
    # 编辑不良品项
    update_product_defects_loc = (By.XPATH, "//span[contains(text(), '编辑')]")
    # 编辑不良品项名称
    update_product_defects_name_loc = (By.XPATH, '//*[@id="name"]')
    # 确定
    update_save_loc = (By.XPATH, "/html/body/div[3]/div/div[2]/div/div[1]/div/div[3]/button[2]")
    # 编辑成功提示
    update_success_loc = (By.XPATH, "//span[contains(text(), '编辑成功！')]")
    # -----------------------------------------删除不良品项-----------------------------------------
    # 删除不良品项按钮
    delete_product_defects_loc = (By.XPATH, "//span[contains(text(), '删除')]")
    # 删除不良品项按钮
    delete_sure_product_defects_loc = (By.XPATH, "/html/body/div[3]/div/div[2]/div/div[1]/div/div/div/div[2]/button[2]")
    # 删除成功提示
    delete_success_loc = (By.XPATH, "//span[contains(text(), '删除成功')]")