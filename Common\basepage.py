"""
1、等待元素可见
2、查找元素
3、点击操作：等待 - 查找 -点击
4、输入操作：等待 - 查找 - 输入
5、获取元素文本：等待 - 查找 - 获取文本
6、获取元素属性：等待 - 查找 - 获取属性值
节省代码量、记录日志、失败截图
"""
import datetime
import sys
from pathlib import Path

from selenium.webdriver.support.wait import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.remote.webdriver import WebDriver
from Common.my_logger import logger
from Common.handle_path import screenshot_dir
from Common.platform_utils import KeyboardUtils, PlatformUtils
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.common.by import By
from selenium.common.exceptions import TimeoutException, NoSuchElementException, NoSuchWindowException, \
    ElementNotInteractableException
import os
import traceback
import time
from datetime import datetime


class Basepage:

    def __init__(self, driver: WebDriver):
        self.driver = driver

    def wait_ele_visible(self, locator, page_action, timeout=20, poll_frequency=0.5):
        """

        :param locator: 定位的元组
        :param page_action:  等待元素可见
        :param timeout:
        :param poll_frequency:
        :return:
        """
        logger.info("在 {} 行为，等待元素： {} 可见。".format(page_action, locator))
        try:
            start = time.time()
            WebDriverWait(self.driver, timeout, poll_frequency).until(EC.visibility_of_element_located(locator))
        except:
            #输出到日志
            logger.exception("等待元素可见失败！")
            #失败截取当前页面
            self.get_page_img(page_action)
            raise
        else:
            end = time.time()
            logger.info("等待耗时为：{}".format(end - start))

    def wait_page_contains_element(self, locator, page_action, timeout=20, poll_frequency=0.5):
        """

        :param locator: 定位的元组
        :param page_action: 等待元素存在
        :param timeout:
        :param poll_frequency:
        :return:
        """
        logger.info("在 {} 行为，等待元素： {} 存在。".format(page_action, locator))
        try:
            start = time.time()
            WebDriverWait(self.driver, timeout, poll_frequency).until(EC.presence_of_element_located(locator))
        except:
            #输出到日志
            logger.exception("等待元素存在失败！")
            #失败截取当前页面
            self.get_page_img(page_action)
            raise
        else:
            end = time.time()
            logger.info("等待耗时为：{}".format(end - start))

    def get_element(self, locator, page_action, timeout=20, poll_frequency=0.5, wait_exist=False):
        #先等待元素可见或者存在，默认是等待元素可见
        # page_action :截图时候保存的页面名字
        if wait_exist is False:
            self.wait_page_contains_element(locator, page_action, timeout, poll_frequency)
        else:
            self.wait_ele_visibile(locator, page_action, timeout, poll_frequency)
        #元素等待或者存在
        logger.info("在 {} 行为，查找元素：{}".format(page_action, locator))
        try:
            ele = self.driver.find_element(*locator)
        except:
            #输出到日志
            logger.exception("查找元素失败！")
            # 失败截取当前页面
            self.get_page_img(page_action)
            raise
        else:
            return ele

    # def click_element(self, locator, page_action, timeout=20, poll_frequency=0.5):
    #     # 等待 - 查找
    #     ele = self.get_element(locator, page_action,timeout, poll_frequency)
    #     # 点击
    #     logger.info("在 {} 行为，点击元素：{}".format(page_action, locator))
    #     try:
    #         ele.click()
    #     except:
    #         logger.exception("点击操作失败！")
    #         self.get_page_img(page_action)
    #         raise

    def click_element(self, locator, page_action, timeout=30, poll_frequency=0.5):
        """
        点击指定元素

        增加显示等待，确保元素可点击（可见且启用）

        :param locator: 定位元素的元组
        :param page_action: 当前页面操作描述
        :param timeout: 超时时间，默认20秒
        :param poll_frequency: 轮询频率，默认0.5秒
        :return: None
        :raises: TimeoutException 如果元素不可点击
        """
        logger.info("在 {} 行为，等待元素可点击：{}".format(page_action, locator))
        try:
            start = time.time()
            # 等待元素可点击（可见且启用）
            ele = WebDriverWait(self.driver, timeout, poll_frequency).until(
                EC.element_to_be_clickable(locator)
            )
            # 执行点击
            ele.click()
        except TimeoutException:
            logger.error("等待元素可点击超时！")
            self.get_page_img(page_action)
            raise
        except Exception as e:
            logger.exception("点击操作失败！错误信息：{}".format(str(e)))
            self.get_page_img(page_action)
            raise
        else:
            end = time.time()
            logger.info("元素点击成功，总耗时：{:.2f}秒".format(end - start))

    # 输入文本
    def input_text(self, locator, page_action, value, timeout=20, poll_frequency=0.5):
        """
        向指定元素输入文本

        增加显示等待，确保元素可交互（可见且启用）

        :param locator: 定位元素的元组
        :param page_action: 当前页面操作描述
        :param value: 要输入的文本值
        :param timeout: 超时时间，默认20秒
        :param poll_frequency: 轮询频率，默认0.5秒
        :return: None
        :raises: TimeoutException 如果元素不可交互
        """
        logger.info("在 {} 行为，等待元素可交互：{}".format(page_action, locator))
        try:
            start = time.time()
            # 等待元素可交互（可见且启用）
            ele = WebDriverWait(self.driver, timeout, poll_frequency).until(
                EC.element_to_be_clickable(locator)
            )
            # 执行输入操作
            logger.info("向元素 {} 输入文本值：{}".format(locator, value))
            ele.clear()
            ele.send_keys(value)
        except TimeoutException:
            logger.error("等待元素可交互超时！")
            self.get_page_img(page_action)
            raise
        except Exception as e:
            logger.exception("元素输入文本失败！错误信息：{}".format(str(e)))
            self.get_page_img(page_action)
            raise
        else:
            end = time.time()
            logger.info("文本输入成功，总耗时：{:.2f}秒".format(end - start))

    # def get_text(self, locator, page_action, timeout=20, poll_frequency=0.5, wait_exist=False):
    #     ele = self.get_element(locator, page_action, timeout, poll_frequency, wait_exist=wait_exist)
    #     logger.info("在 {} 行为，获取元素 {} 的文本值。".format(page_action, locator))
    #     try:
    #         txt = ele.text
    #     except:
    #         logger.exception("获取元素文本失败！")
    #         self.get_page_img(page_action)
    #         raise
    #     else:
    #         logger.info("文本值为：{}".format(txt))
    #         return txt

    def get_text(self, locator, page_action, timeout=30, poll_frequency=0.5, wait_exist=False):
        """
        获取指定元素的文本内容

        :param locator: 定位元素的元组
        :param page_action: 当前页面操作描述
        :param timeout: 超时时间，默认20秒
        :param poll_frequency: 轮询频率，默认0.5秒
        :param wait_exist: 是否只等待元素存在而不检查可见性，默认False（检查可见性）
        :return: 元素的文本内容
        :raises: TimeoutException 如果元素不可见/不存在
        """
        logger.info("在 {} 行为，等待元素并获取文本：{}".format(page_action, locator))

        try:
            start = time.time()

            # 根据wait_exist参数选择不同的等待条件
            if wait_exist:
                # 只等待元素存在（不检查可见性）
                ele = WebDriverWait(self.driver, timeout, poll_frequency).until(
                    EC.presence_of_element_located(locator)
                )
            else:
                # 默认等待元素可见
                ele = WebDriverWait(self.driver, timeout, poll_frequency).until(
                    EC.visibility_of_element_located(locator)
                )

            # 获取文本内容
            txt = ele.text
            logger.debug("获取到的元素文本内容：{}".format(txt))

        except TimeoutException:
            error_msg = "等待元素{}超时！".format("存在" if wait_exist else "可见")
            logger.error(error_msg)
            self.get_page_img(page_action)
            raise
        except Exception as e:
            logger.exception("获取元素文本失败！错误信息：{}".format(str(e)))
            self.get_page_img(page_action)
            raise
        else:
            end = time.time()
            logger.info("成功获取文本[{}]，总耗时：{:.2f}秒".format(txt, end - start))
            return txt

    # def get_attribute(self, locator, page_action, attr, timeout=20, poll_frequency=0.5, wait_exist=False):
    #     ele = self.get_element(locator, page_action, timeout, poll_frequency, wait_exist=wait_exist)
    #     logger.info("在 {} 行为，获取元素 {} 的 {} 属性值。".format(page_action, locator, attr))
    #     try:
    #         value = ele.get_attribute(attr)
    #     except:
    #         logger.exception("获取元素属性失败！")
    #         self.get_page_img(page_action)
    #         raise
    #     else:
    #         logger.info("属性值为：{}".format(value))
    #         return value

    def get_attribute(self, locator, page_action, attr, timeout=20, poll_frequency=0.5, wait_exist=False):
        """
        获取指定元素的属性值

        :param locator: 定位元素的元组
        :param page_action: 当前页面操作描述
        :param attr: 要获取的属性名称
        :param timeout: 超时时间，默认20秒
        :param poll_frequency: 轮询频率，默认0.5秒
        :param wait_exist: 是否只等待元素存在而不检查可见性，默认False（检查可见性）
        :return: 元素的属性值
        :raises: TimeoutException 如果元素不可见/不存在
                 NoSuchElementException 如果元素不存在
                 AttributeError 如果属性不存在
        """
        logger.info("在 {} 行为，获取元素 {} 的 {} 属性值".format(page_action, locator, attr))

        try:
            start = time.time()

            # 根据wait_exist参数选择不同的等待条件
            if wait_exist:
                # 只等待元素存在（不检查可见性）
                ele = WebDriverWait(self.driver, timeout, poll_frequency).until(
                    EC.presence_of_element_located(locator)
                )
            else:
                # 默认等待元素可见
                ele = WebDriverWait(self.driver, timeout, poll_frequency).until(
                    EC.visibility_of_element_located(locator)
                )

            # 获取属性值
            value = ele.get_attribute(attr)
            logger.debug("获取到的元素属性[{}]值：{}".format(attr, value))

        except TimeoutException:
            error_msg = "等待元素{}超时！".format("存在" if wait_exist else "可见")
            logger.error(error_msg)
            self.get_page_img(page_action)
            raise
        except NoSuchElementException:
            logger.error("元素 {} 不存在！".format(locator))
            self.get_page_img(page_action)
            raise
        except AttributeError:
            logger.error("元素 {} 不存在 {} 属性！".format(locator, attr))
            self.get_page_img(page_action)
            raise
        except Exception as e:
            logger.exception("获取元素属性失败！错误信息：{}".format(str(e)))
            self.get_page_img(page_action)
            raise
        else:
            end = time.time()
            logger.info("成功获取属性[{}]值[{}]，总耗时：{:.2f}秒".format(attr, value, end - start))
            return value

    # def switch_windows(self):
    #     # , name = "new"
    #     #等待一下
    #     time.sleep(1)
    #     wins = self.driver.window_handles
    #     # if name == "new":
    #     #     driver.switch_to.window(wins[-1])
    #     self.driver.switch_to.window(wins[-1])

    def switch_windows(self, window_index=-1, timeout=10, poll_frequency=0.5):
        """
        切换到指定窗口（默认切换到最新打开的窗口）

        :param window_index: 窗口索引，默认为-1（最新窗口）
        :param timeout: 等待新窗口出现的超时时间，默认10秒
        :param poll_frequency: 轮询频率，默认0.5秒
        :raises: TimeoutException 如果超时未找到新窗口
                 NoSuchWindowException 如果窗口不存在
        """
        current_window = self.driver.current_window_handle
        logger.info(f"准备切换窗口，当前窗口句柄：{current_window}")

        try:
            # 使用智能等待替代固定sleep
            def new_window_available(driver):
                return len(driver.window_handles) > len([current_window])

            WebDriverWait(self.driver, timeout, poll_frequency).until(
                new_window_available,
                message=f"在{timeout}秒内未检测到新窗口出现"
            )

            windows = self.driver.window_handles
            logger.debug(f"当前所有窗口句柄：{windows}")

            if window_index >= len(windows):
                raise IndexError(f"窗口索引{window_index}超出范围，总窗口数：{len(windows)}")

            target_window = windows[window_index]
            self.driver.switch_to.window(target_window)
            logger.info(f"已切换到窗口[{window_index}]，句柄：{target_window}")

        except TimeoutException:
            logger.error(f"等待{timeout}秒后仍未出现新窗口")
            self.get_page_img("switch_window_timeout")
            raise
        except NoSuchWindowException:
            logger.error(f"目标窗口[{window_index}]不存在")
            self.get_page_img("switch_window_not_exist")
            raise
        except IndexError as e:
            logger.error(str(e))
            self.get_page_img("switch_window_index_error")
            raise
        except Exception as e:
            logger.exception(f"切换窗口失败：{str(e)}")
            self.get_page_img("switch_window_failed")
            raise

    def back_last_page(self):
        logger.info("回退到上一页")
        self.driver.back()
        return self

    def refresh_page(self):
        logger.info("刷新当前页面")
        self.driver.refresh()
        return self

    def switch_to_form(self, locator, page_action, timeout=20, poll_frequency=0.5):
        """
        切换到指定的表单（iframe/frame）

        增加显式等待，确保表单存在并可切换

        :param locator: 定位表单的元组（支持iframe/frame的常规定位方式）
        :param page_action: 当前页面操作描述
        :param timeout: 超时时间，默认20秒
        :param poll_frequency: 轮询频率，默认0.5秒
        :return: None
        :raises: TimeoutException 如果表单不可用
        """
        logger.info("在 {} 行为，尝试切换到表单：{}".format(page_action, locator))
        try:
            start = time.time()
            # 等待表单元素存在
            form = WebDriverWait(self.driver, timeout, poll_frequency).until(
                EC.frame_to_be_available_and_switch_to_it(locator)
            )
            logger.info("成功切换到表单：{}".format(locator))
        except TimeoutException:
            logger.error("等待表单加载超时！")
            self.get_page_img(page_action)
            raise
        except Exception as e:
            logger.exception("切换表单失败！错误信息：{}".format(str(e)))
            self.get_page_img(page_action)
            raise
        else:
            end = time.time()
            logger.info("表单切换成功，总耗时：{:.2f}秒".format(end - start))

    # def get_page_img(self,page_action):
    #
    #     #截图命名：{XX页面_XX操作}_截图时间.png
    #     cur_time = time.strftime('%Y-%m-%d-%H-%M-%S', time.localtime())
    #     file_path = os.path.join(screenshot_dir, "{}_{}.png".format(page_action, cur_time))
    #     self.driver.save_screenshot(file_path)
    #     logger.info("截图保存在：{}".format(file_path))

    # def get_page_img(self, page_action):
    #     """
    #     页面截图方法（带完整错误处理）
    #
    #     :param page_action: 操作描述（用于文件名）
    #     :return: 截图文件路径（失败时返回None）
    #     """
    #     try:
    #         # 1. 确保截图目录存在
    #         os.makedirs(screenshot_dir, exist_ok=True)
    #
    #         # 2. 生成带时间戳的文件名
    #         cur_time = time.strftime('%Y%m%d_%H%M%S')
    #         filename = f"{page_action}_{cur_time}.png"
    #         file_path = os.path.abspath(os.path.join(screenshot_dir, filename))
    #
    #         # 3. 执行截图
    #         self.driver.save_screenshot(file_path)
    #         logger.success(f"截图成功：{file_path}")
    #         return file_path
    #
    #     except Exception as e:
    #         # 4. 失败处理
    #         error_msg = f"截图失败：{str(e)}"
    #         logger.error(error_msg, exc_info=True)
    #
    #         # 5. 尝试备用截图方案
    #         try:
    #             fallback_path = os.path.join(screenshot_dir, "FAILSAFE.png")
    #             self.driver.save_screenshot(fallback_path)
    #             logger.warning(f"使用备用路径保存截图：{fallback_path}")
    #             return fallback_path
    #         except:
    #             logger.critical("所有截图方案均失败！")
    #             return None

    # def clear_text(self, locator, page_action, timeout=20, poll_frequency=0.5):
    #     # 等待 - 查找
    #     ele = self.get_element(locator, page_action, timeout, poll_frequency)
    #     # logger.info("在 {} 清理行为，给元素：{}".format(page_action, locator))
    #     logger.info(f"在 {page_action} 行为，清除元素：{locator} 的文本内容")
    #     try:
    #         # ele.clear()
    #         ele.send_keys(Keys.CONTROL + "a")  # Windows/Linux
    #         # ele.send_keys(Keys.COMMAND + "a")  # Mac
    #         ele.send_keys(Keys.DELETE)
    #     except:
    #         logger.exception("元素输入文本失败！")
    #         self.get_page_img(page_action)
    #         raise
    # 清空文本框

    # def get_page_img(self, page_action):
    #     """
    #     精确到分钟的增强版截图方法
    #
    #     :param page_action: 操作描述（自动清理特殊字符）
    #     :return: (success_status: bool, file_path: str)
    #     """
    #     try:
    #         # 1. 安全处理操作描述
    #         safe_action = "".join(c if c.isalnum() else "_" for c in str(page_action))
    #
    #         # 2. 基础路径配置（支持环境变量覆盖）
    #         base_dir = Path(os.getenv("SCREENSHOT_DIR", "reports/screenshots"))
    #         time_str = datetime.now().strftime("%Y-%m-%d_%H%M")  # 精确到分钟
    #
    #         # 3. 创建分类目录结构
    #         target_dir = base_dir / time_str
    #         target_dir.mkdir(parents=True, exist_ok=True)
    #
    #         # 4. 生成唯一文件名（添加毫秒防止冲突）
    #         timestamp = datetime.now().strftime("%H%M%S_%f")[:-3]  # 精确到毫秒
    #         filename = f"{safe_action}_{timestamp}.png"
    #         file_path = target_dir / filename
    #
    #         # 5. 直接截图到目标位置
    #         self.driver.save_screenshot(str(file_path))
    #
    #         logger.info(f"截图成功：{file_path}")
    #         return True, str(file_path)
    #
    #     except Exception as e:
    #         # 6. 增强错误处理
    #         error_time = datetime.now().strftime("%Y-%m-%d_%H%M%S")
    #         error_log = screenshot_dir / "error_logs" / f"fail_{error_time}.log"
    #
    #         error_log.parent.mkdir(exist_ok=True)
    #         with open(error_log, "w") as f:
    #             f.write(f"Failed action: {screenshot_dir}\n")
    #             f.write(f"Error: {type(e).__name__}: {str(e)}\n")
    #             f.write(f"Traceback:\n{screenshot_dir.format_exc()}")
    #
    #         logger.error(f"截图失败！错误日志：{error_log}")
    #         return False, str(error_log)

    def get_page_img(self, page_action, session_mark=None):
        """
        严格匹配项目结构的截图方法

        :param page_action: 操作描述（自动清理特殊字符）
        :param session_mark: 可选会话标记（用于归类同流程截图）
        :return: (success: bool, path: str)
        """
        try:
            # 1. 使用项目配置的截图路径
            screenshot_path = Path(screenshot_dir)

            # 2. 创建带时间戳的目录结构
            date_str = datetime.now().strftime("%Y%m%d")
            daily_dir = screenshot_path / date_str
            daily_dir.mkdir(exist_ok=True)

            # 3. 生成标准化文件名
            time_str = datetime.now().strftime("%H%M%S")
            safe_action = "".join(c if c.isalnum() else "_" for c in str(page_action))
            filename = f"{safe_action}_{time_str}.png"

            # 4. 执行截图
            file_path = daily_dir / filename
            self.driver.save_screenshot(str(file_path))

            logger.info(f"截图保存到：{file_path}")
            return True, str(file_path)

        except Exception as e:
            # 5. 错误处理（直接存到screenshots/error_logs）
            error_path = Path(screenshot_dir) / "error_logs"
            error_path.mkdir(exist_ok=True)

            error_file = error_path / f"error_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log"
            with open(error_file, "w", encoding='utf-8') as f:
                f.write(f"TIMESTAMP: {datetime.now()}\n")
                f.write(f"ACTION: {page_action}\n")
                f.write(f"ERROR: {type(e).__name__}\n")
                f.write(f"DETAIL:\n{traceback.format_exc()}\n")

            logger.error(f"截图失败！错误日志：{error_file}")
            return False, str(error_file)

    def clear_text(self, locator, page_action, timeout=20, poll_frequency=0.5):
        """
        清除指定元素的文本内容（跨平台支持）

        使用全选+删除的方式清除文本，适用于各种输入场景
        自动检测平台并使用相应的快捷键组合

        :param locator: 定位元素的元组
        :param page_action: 当前页面操作描述
        :param timeout: 超时时间，默认20秒
        :param poll_frequency: 轮询频率，默认0.5秒
        :raises: TimeoutException 如果元素不可交互
                 ElementNotInteractableException 如果元素不可操作
        """
        platform_type = PlatformUtils.get_platform()
        logger.info(f"在 {page_action} 行为，准备清除元素 {locator} 的文本内容 (平台: {platform_type.value})")

        try:
            start = time.time()

            # 等待元素可交互
            ele = WebDriverWait(self.driver, timeout, poll_frequency).until(
                EC.element_to_be_clickable(locator)
            )

            # 记录原始文本（用于调试）
            original_text = ele.get_attribute('value') or ele.text
            logger.debug(f"元素原始文本内容：{original_text}")

            # 使用平台工具获取正确的修饰键
            modifier_key = KeyboardUtils.get_selenium_modifier_key()
            shortcut_name = KeyboardUtils.get_select_all_key()

            # 执行全选操作
            ele.send_keys(modifier_key + "a")
            logger.debug(f"使用{platform_type.value}平台全选快捷键: {shortcut_name}")

            # 删除选中的文本
            ele.send_keys(Keys.DELETE)

            # 验证是否清除成功
            cleared_text = ele.get_attribute('value') or ele.text
            if cleared_text and cleared_text.strip():
                logger.warning(f"文本清除后仍存在内容：{cleared_text}")
                # 尝试直接clear()方法作为后备方案
                try:
                    ele.clear()
                    logger.debug("使用备用clear()方法清除文本")
                except Exception as clear_error:
                    logger.warning(f"备用clear()方法也失败：{clear_error}")
                    # 最后尝试多次删除
                    for _ in range(3):
                        ele.send_keys(Keys.BACKSPACE * 50)
                        ele.send_keys(Keys.DELETE * 50)

            # 最终验证
            final_text = ele.get_attribute('value') or ele.text
            if final_text and final_text.strip():
                logger.warning(f"文本清除可能不完全，剩余内容：{final_text}")
            else:
                logger.debug("文本清除验证成功")

        except TimeoutException:
            logger.error(f"等待元素可交互超时（{timeout}秒）")
            self.get_page_img(f"{page_action}_clear_timeout")
            raise
        except ElementNotInteractableException:
            logger.error("元素存在但不可交互")
            self.get_page_img(f"{page_action}_element_not_interactable")
            raise
        except Exception as e:
            logger.exception(f"清除文本失败！错误类型：{type(e).__name__}，信息：{str(e)}")
            self.get_page_img(f"{page_action}_clear_failed")
            raise
        else:
            end = time.time()
            logger.info(f"成功清除元素文本，耗时：{end - start:.2f}秒")

    def scroll_page(self, locator=None, page_action="", direction=None, offset=0, timeout=20):
        """

        :param locator: 元素定位器（可选），如果提供则滚动到该元素
        :param page_action: 当前页面行为描述（用于日志）
        :param direction: 滚动方向（'up', 'down', 'left', 'right'），与locator互斥
        :param offset: 滚动像素偏移量（正数向下/右，负数向上/左）
        :param timeout: 查找元素的超时时间（仅当locator存在时生效）
        :raises: 如果操作失败，记录日志并抛出异常
        """
        logger.info(f"在 {page_action} 行为，执行滚动操作 - 方向: {direction}, 元素: {locator}")

        try:
            # 情况1：滚动到指定元素
            if locator:
                ele = self.get_element(locator, page_action, timeout)
                self.driver.execute_script("arguments[0].scrollIntoView({behavior: 'smooth'});", ele)

            # 情况2：按方向滚动
            elif direction:
                js_code = {
                    'up': "window.scrollBy(0, -arguments[0]);",
                    'down': "window.scrollBy(0, arguments[0]);",
                    'left': "window.scrollBy(-arguments[0], 0);",
                    'right': "window.scrollBy(arguments[0], 0);"
                }.get(direction.lower())

                if js_code:
                    self.driver.execute_script(js_code, abs(offset))
                else:
                    raise ValueError(f"不支持的滚动方向: {direction}")

            # 情况3：无参数默认滚动到页面底部
            else:
                self.driver.execute_script("window.scrollTo(0, document.body.scrollHeight);")

        except Exception as e:
            logger.exception(f"滚动页面失败！操作: {page_action}")
            self.get_page_img(page_action)
            raise

    def scroll_page_half(self, locator=None, page_action="", direction=None, offset=0, timeout=20, scroll_percent=None):
        """
        :param locator: 元素定位器（可选），如果提供则滚动到该元素
        :param page_action: 当前页面行为描述（用于日志）
        :param direction: 滚动方向（'up', 'down', 'left', 'right'），与locator互斥
        :param offset: 滚动像素偏移量（正数向下/右，负数向上/左）
        :param timeout: 查找元素的超时时间（仅当locator存在时生效）
        :param scroll_percent: 滚动百分比（0-1，如0.5表示50%），与direction/locator互斥
        :raises: 如果操作失败，记录日志并抛出异常
        """
        logger.info(
            f"在 {page_action} 行为，执行滚动操作 - 方向: {direction}, 元素: {locator}, 百分比: {scroll_percent}")

        try:
            # 情况1：滚动到指定百分比（优先级最高）
            if scroll_percent is not None:
                if not 0 <= scroll_percent <= 1:
                    raise ValueError("scroll_percent 必须在 0 到 1 之间")
                js = """
                    const height = document.body.scrollHeight;
                    window.scrollTo(0, height * arguments[0]);
                """
                self.driver.execute_script(js, scroll_percent)

            # 情况2：滚动到指定元素
            elif locator:
                ele = self.get_element(locator, page_action, timeout)
                self.driver.execute_script("arguments[0].scrollIntoView({behavior: 'smooth'});", ele)

            # 情况3：按方向滚动
            elif direction:
                js_code = {
                    'up': "window.scrollBy(0, -arguments[0]);",
                    'down': "window.scrollBy(0, arguments[0]);",
                    'left': "window.scrollBy(-arguments[0], 0);",
                    'right': "window.scrollBy(arguments[0], 0);"
                }.get(direction.lower())
                if js_code:
                    self.driver.execute_script(js_code, abs(offset))
                else:
                    raise ValueError(f"不支持的滚动方向: {direction}")

            # 情况4：无参数默认滚动到页面底部
            else:
                self.driver.execute_script("window.scrollTo(0, document.body.scrollHeight);")

        except Exception as e:
            logger.exception(f"滚动页面失败！操作: {page_action}")
            self.get_page_img(page_action)
            raise


# if __name__ == '__main__':
#     from selenium import webdriver
#     from time import sleep
#
#     driver = webdriver.Chrome()
#     base = Basepage(driver)
#     driver.get("http://www.baidu.com")
#     # 等待输入框可见
#     loc = ("id", "kw")
#     base.input_text(loc, "百度首页-等待搜索输入框可见", "selenium")
#     loc_button = ("id","su")
#     base.click_element(loc_button,"百度搜索页")
#     sleep(7)
#     driver.quit()
